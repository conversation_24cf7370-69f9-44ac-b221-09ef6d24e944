<!DOCTYPE html>
<html lang="en">
	<head>
		<meta charset="utf-8" />
		<link rel="icon" href="%sveltekit.assets%/favicon.png" />
		<meta name="viewport" content="width=device-width, initial-scale=1" />

		<!-- PWA Meta Tags -->
		<meta name="application-name" content="Cinemated" />
		<meta name="apple-mobile-web-app-capable" content="yes" />
		<meta name="apple-mobile-web-app-status-bar-style" content="default" />
		<meta name="apple-mobile-web-app-title" content="Cinemated" />
		<meta name="description" content="Get personalized movie and TV show recommendations powered by AI technology" />
		<meta name="format-detection" content="telephone=no" />
		<meta name="mobile-web-app-capable" content="yes" />
		<meta name="msapplication-config" content="%sveltekit.assets%/browserconfig.xml" />
		<meta name="msapplication-TileColor" content="#1de9b6" />
		<meta name="msapplication-tap-highlight" content="no" />
		<meta name="theme-color" content="#1de9b6" />

		<!-- Apple Touch Icons -->
		<link rel="apple-touch-icon" href="%sveltekit.assets%/icon-152x152.png" />
		<link rel="apple-touch-icon" sizes="152x152" href="%sveltekit.assets%/icon-152x152.png" />
		<link rel="apple-touch-icon" sizes="180x180" href="%sveltekit.assets%/icon-192x192.png" />

		<!-- Manifest -->
		<link rel="manifest" href="%sveltekit.assets%/manifest.json" />

		<!-- Favicon -->
		<link rel="icon" type="image/png" sizes="32x32" href="%sveltekit.assets%/favicon.png" />
		<link rel="shortcut icon" href="%sveltekit.assets%/favicon.png" />

		<!-- Place this tag in your head or just before your close body tag. -->
		<script async defer src="https://buttons.github.io/buttons.js"></script>
		%sveltekit.head%
	</head>
	<body data-sveltekit-preload-data="hover">
		<div style="display: contents">%sveltekit.body%</div>
	</body>
</html>
