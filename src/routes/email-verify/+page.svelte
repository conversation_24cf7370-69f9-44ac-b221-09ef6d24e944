<script>
	import { onMount } from 'svelte';
	import { page } from '$app/stores';
	import { goto } from '$app/navigation';
	import { fade } from 'svelte/transition';

	let loading = true;
	let success = false;
	let error = '';
	let userEmail = '';

	onMount(async () => {
		const code = $page.url.searchParams.get('code');
		
		if (!code) {
			error = 'Invalid verification link. No verification code provided.';
			loading = false;
			return;
		}

		try {
			const response = await fetch('/api/email-verify', {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json'
				},
				body: JSON.stringify({ code })
			});

			const data = await response.json();

			if (response.ok && data.success) {
				success = true;
				userEmail = data.email;
			} else {
				error = data.message || 'Verification failed. Please try again.';
			}
		} catch (err) {
			error = 'Network error. Please check your connection and try again.';
		}

		loading = false;
	});

	function goToLogin() {
		goto('/');
	}
</script>

<svelte:head>
	<title>Email Verification - Cinemated</title>
</svelte:head>

<div>
	<div class="h-screen w-full bg-cover fixed" style="background-image: url(/background.png)">
		<div class="flex flex-col items-center justify-center min-h-screen w-full h-full bg-gradient-to-br from-slate-900/80 to-black/90" />
	</div>

	<div class="absolute inset-0 px-6 flex flex-col h-screen">
		<div class="flex-grow flex items-center justify-center">
			<div class="max-w-md w-full">
				{#if loading}
					<div in:fade class="text-center">
						<div class="bg-white/10 backdrop-blur-sm rounded-lg p-8">
							<div class="animate-spin rounded-full h-12 w-12 border-b-2 border-turquoise-500 mx-auto mb-4"></div>
							<h1 class="text-2xl font-bold text-white mb-2">Verifying Email</h1>
							<p class="text-white/70">Please wait while we verify your email address...</p>
						</div>
					</div>
				{:else if success}
					<div in:fade class="text-center">
						<div class="bg-white/10 backdrop-blur-sm rounded-lg p-8">
							<div class="w-16 h-16 bg-green-500 rounded-full flex items-center justify-center mx-auto mb-4">
								<svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
									<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
								</svg>
							</div>
							<h1 class="text-2xl font-bold text-white mb-2">Email Verified!</h1>
							<p class="text-white/70 mb-6">
								Your email address <span class="text-turquoise-400 font-medium">{userEmail}</span> has been successfully verified.
							</p>
							<p class="text-white/70 mb-6">
								You can now login to your account and start getting movie recommendations!
							</p>
							<button
								on:click={goToLogin}
								class="bg-turquoise-600 hover:bg-turquoise-700 text-white font-medium py-3 px-6 rounded-md w-full"
							>
								Go to Login
							</button>
						</div>
					</div>
				{:else}
					<div in:fade class="text-center">
						<div class="bg-white/10 backdrop-blur-sm rounded-lg p-8">
							<div class="w-16 h-16 bg-red-500 rounded-full flex items-center justify-center mx-auto mb-4">
								<svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
									<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
								</svg>
							</div>
							<h1 class="text-2xl font-bold text-white mb-2">Verification Failed</h1>
							<p class="text-red-300 mb-6">{error}</p>
							<div class="space-y-3">
								<button
									on:click={goToLogin}
									class="bg-turquoise-600 hover:bg-turquoise-700 text-white font-medium py-3 px-6 rounded-md w-full"
								>
									Go to Login
								</button>
								<p class="text-white/50 text-sm">
									If you continue to have issues, please contact support.
								</p>
							</div>
						</div>
					</div>
				{/if}
			</div>
		</div>
	</div>
</div>
