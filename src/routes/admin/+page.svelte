<script>
	import { onMount } from 'svelte';
	import { goto } from '$app/navigation';
	import AdminPanel from '$lib/AdminPanel.svelte';

	let userData = null;
	let isLoggedIn = false;
	let loading = true;

	onMount(() => {
		// Check if user is logged in and is admin
		const storedUser = localStorage.getItem('user');
		if (storedUser) {
			try {
				userData = JSON.parse(storedUser);
				isLoggedIn = true;
				
				// Check if user is admin
				if (!userData.isAdmin) {
					// Redirect non-admin users to home page
					goto('/');
					return;
				}
			} catch (err) {
				localStorage.removeItem('user');
				goto('/');
				return;
			}
		} else {
			// Redirect non-logged-in users to home page
			goto('/');
			return;
		}
		
		loading = false;
	});

	function handleLogout() {
		localStorage.removeItem('user');
		goto('/');
	}
</script>

<svelte:head>
	<title>Admin Panel - Cinemated</title>
</svelte:head>

{#if loading}
	<div class="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 flex items-center justify-center">
		<div class="text-white text-xl">Loading...</div>
	</div>
{:else if isLoggedIn && userData?.isAdmin}
	<AdminPanel {userData} on:logout={handleLogout} />
{:else}
	<div class="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 flex items-center justify-center">
		<div class="text-white text-xl">Access Denied</div>
	</div>
{/if}
