import { json } from '@sveltejs/kit';
import { API_KEY_DEV, API_KEY_PROD, API_URL_DEV, API_URL_PROD, ENVIRONMENT } from '$env/static/private';

export async function POST({ request }: { request: any }) {
	const { title, year, userCountry } = await request.json();

	// Get user IP address
	let userIP =
		request.headers.get('x-forwarded-for')?.split(',')[0]?.trim() ||
		request.headers.get('cf-connecting-ip') ||
		request.headers.get('x-real-ip') ||
		(request as any).ip || // Some frameworks attach ip directly
		'127.0.0.1';

	// Determine which API key and URL to use based on environment
	const apiKey = ENVIRONMENT === 'PROD' ? API_KEY_PROD : API_KEY_DEV;
	const baseUrl = ENVIRONMENT === 'PROD' ? API_URL_PROD : API_URL_DEV;
	const apiUrl = `${baseUrl}/api/searchmovies`;

	// Send POST request with title, year, user IP, and user country in the body
	const res = await fetch(apiUrl, {
		method: 'POST',
		headers: {
			'Content-Type': 'application/json',
			'x-api-key': apiKey
		},
		body: JSON.stringify({
			title: title,
			year: year,
			userIP: userIP,
			userCountry: userCountry || 'US' // Default to US if not provided
		})
	});

	if (!res.ok) {
		return new Response(`API request failed: ${res.statusText}`, { status: res.status });
	}

	const details = await res.json();

	return json(details);
}
