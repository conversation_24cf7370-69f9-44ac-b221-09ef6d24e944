
import { API_KEY_DEV, API_KEY_PROD, API_URL_DEV, API_URL_PROD, ENVIRONMENT } from '$env/static/private';

import { json } from '@sveltejs/kit';
import { connectToDatabase } from '$lib/mongodb';
import { upstashRateLimit } from '$lib/upstash';

// User authentication function
async function validateUser(request: Request) {
	const userEmail = request.headers.get('x-user-email');

	if (!userEmail) {
		return null;
	}

	try {
		const db = await connectToDatabase();
		const user = await db.collection('users').findOne({
			email: userEmail,
			isEnabled: true
		});

		return user;
	} catch (error) {
		console.error('Error validating user:', error);
		return null;
	}
}

export async function POST({ request }: { request: any }) {
	// 1. Validate user authentication
	const user = await validateUser(request);
	if (!user) {
		return new Response('Unauthorized: Please login to get recommendations', { status: 401 });
	}

	// Get user IP address
	let userIP =
		request.headers.get('x-forwarded-for')?.split(',')[0]?.trim() ||
		request.headers.get('cf-connecting-ip') ||
		request.headers.get('x-real-ip') ||
		(request as any).ip || // Some frameworks attach ip directly
		'127.0.0.1';

	// 2. Check rate limiting based on IP address
	try {
		const rateLimitResult = await upstashRateLimit.checkAndUpdateRateLimit(userIP);
		if (!rateLimitResult.allowed) {
			return new Response('Rate limit exceeded: Maximum 50 requests per hour allowed', { status: 429 });
		}
	} catch (error) {
		console.error('Rate limiting error:', error);
		// Continue without rate limiting if Upstash is unavailable
	}

	const { searched, userCountry } = await request.json();

	// Determine which API key and URL to use based on environment
	const apiKey = ENVIRONMENT === 'PROD' ? API_KEY_PROD : API_KEY_DEV;
	const baseUrl = ENVIRONMENT === 'PROD' ? API_URL_PROD : API_URL_DEV;
	const apiUrl = `${baseUrl}/api/openai`;

	// Send POST request with searched text, user IP, and user country
	const res = await fetch(apiUrl, {
		method: 'POST',
		headers: {
			'Content-Type': 'application/json',
			'x-api-key': apiKey
		},
		body: JSON.stringify({
			searched: searched,
			userIP: userIP,
			userCountry: userCountry || 'US' // Default to US if not provided
		})
	});

	if (!res.ok) {
		return new Response(`API request failed: ${res.statusText}`, { status: res.status });
	}

	const details = await res.json();

	return json(details);
}

