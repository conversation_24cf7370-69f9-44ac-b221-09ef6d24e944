import { json } from '@sveltejs/kit';
import { connectToDatabase } from '$lib/mongodb';
import { upstashRateLimit } from '$lib/upstash';

// User authentication function
async function validateUser(request: Request) {
	const userEmail = request.headers.get('x-user-email');

	if (!userEmail) {
		return null;
	}

	try {
		const db = await connectToDatabase();
		const user = await db.collection('users').findOne({
			email: userEmail,
			isEnabled: true
		});

		return user;
	} catch (error) {
		console.error('Error validating user:', error);
		return null;
	}
}

export async function GET({ request }: { request: any }) {
	// 1. Validate user authentication
	const user = await validateUser(request);
	if (!user) {
		return new Response('Unauthorized: Please login to get recommendation count', { status: 401 });
	}

	// Get user IP address
	let userIP =
		request.headers.get('x-forwarded-for')?.split(',')[0]?.trim() ||
		request.headers.get('cf-connecting-ip') ||
		request.headers.get('x-real-ip') ||
		(request as any).ip || // Some frameworks attach ip directly
		'127.0.0.1';

	try {
		const countData = await upstashRateLimit.getCurrentCount(userIP);
		return json({
			count: countData.count,
			resetTime: countData.resetTime,
			maxRequests: 50
		});
	} catch (error) {
		console.error('Error getting recommendation count:', error);
		return json({
			count: 0,
			resetTime: Date.now(),
			maxRequests: 50
		});
	}
}
