import { json } from '@sveltejs/kit';
import { connectToDatabase } from '$lib/mongodb';
import { ADMIN_EMAIL } from '$env/static/private';
import { generateSecretKey, generateVerificationCode } from '$lib/utils/secretKey';

// Middleware to check if user is admin
async function checkAdminAccess(request: Request) {
	const authHeader = request.headers.get('authorization');
	if (!authHeader || !authHeader.startsWith('Admin ')) {
		return false;
	}

	const adminEmail = authHeader.substring(6); // Remove "Admin " prefix
	return adminEmail === ADMIN_EMAIL;
}

// POST - Regenerate verification code for existing user
export async function POST({ request }: { request: Request }) {
	// Check admin access
	if (!await checkAdminAccess(request)) {
		return new Response('Unauthorized', { status: 401 });
	}

	const { email } = await request.json();

	if (!email) {
		return new Response('Email is required', { status: 400 });
	}

	try {
		const db = await connectToDatabase();
		const usersCollection = db.collection('users');

		// Check if user exists and is not verified
		const user = await usersCollection.findOne({ 
			email: email,
			emailVerified: false
		});

		if (!user) {
			return new Response('User not found or already verified', { status: 404 });
		}

		// Generate new verification code and secret key
		const verificationCode = generateVerificationCode();
		const newSecretKey = generateSecretKey();

		// Update user with new verification code and secret key
		await usersCollection.updateOne(
			{ email: email },
			{
				$set: {
					verificationCode: verificationCode,
					secretKey: newSecretKey,
					verificationCodeGeneratedAt: new Date()
				}
			}
		);

		return json({
			success: true,
			message: 'Verification code and secret key regenerated successfully',
			email: email,
			verificationCode: verificationCode,
			secretKey: newSecretKey
		});

	} catch (error) {
		console.error('Error regenerating verification code:', error);
		return new Response('Internal server error', { status: 500 });
	}
}
