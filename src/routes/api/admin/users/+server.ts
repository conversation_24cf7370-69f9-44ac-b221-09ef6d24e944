import { json } from '@sveltejs/kit';
import { connectToDatabase } from '$lib/mongodb';
import { ADMIN_EMAIL } from '$env/static/private';
import { ObjectId } from 'mongodb';

// Middleware to check if user is admin
async function checkAdminAccess(request: Request) {
	const authHeader = request.headers.get('authorization');
	if (!authHeader || !authHeader.startsWith('Admin ')) {
		return false;
	}

	const adminEmail = authHeader.substring(6); // Remove "Admin " prefix
	return adminEmail === ADMIN_EMAIL;
}

// GET - List all users
export async function GET({ request }: { request: Request }) {
	// Check admin access
	if (!await checkAdminAccess(request)) {
		return new Response('Unauthorized', { status: 401 });
	}

	try {
		const db = await connectToDatabase();
		const usersCollection = db.collection('users');

		// Get all users with relevant information
		const users = await usersCollection
			.find({}, {
				projection: {
					secretKey: 0 // Exclude secret key from response
				}
			})
			.sort({ createdAt: -1 })
			.toArray();

		return json({
			success: true,
			users: users
		});

	} catch (error) {
		console.error('Error fetching users:', error);
		return new Response('Internal server error', { status: 500 });
	}
}

// POST - Update user status (enable/disable)
export async function POST({ request }: { request: Request }) {
	// Check admin access
	if (!await checkAdminAccess(request)) {
		return new Response('Unauthorized', { status: 401 });
	}

	const { userId, action, isEnabled } = await request.json();

	if (!userId || !action) {
		return new Response('Missing required fields', { status: 400 });
	}

	try {
		const db = await connectToDatabase();
		const usersCollection = db.collection('users');

		if (action === 'toggle_status') {
			// Toggle user enabled/disabled status
			const result = await usersCollection.updateOne(
				{ _id: new ObjectId(userId) },
				{
					$set: {
						isEnabled: isEnabled,
						statusUpdatedAt: new Date(),
						statusUpdatedBy: 'admin'
					}
				}
			);

			if (result.matchedCount === 0) {
				return new Response('User not found', { status: 404 });
			}

			return json({
				success: true,
				message: `User ${isEnabled ? 'enabled' : 'disabled'} successfully`
			});

		} else if (action === 'delete') {
			// Delete user (soft delete by marking as deleted)
			const result = await usersCollection.updateOne(
				{ _id: new ObjectId(userId) },
				{
					$set: {
						isDeleted: true,
						isEnabled: false,
						deletedAt: new Date(),
						deletedBy: 'admin'
					}
				}
			);

			if (result.matchedCount === 0) {
				return new Response('User not found', { status: 404 });
			}

			return json({
				success: true,
				message: 'User deleted successfully'
			});
		}

		return new Response('Invalid action', { status: 400 });

	} catch (error) {
		console.error('Error updating user:', error);
		return new Response('Internal server error', { status: 500 });
	}
}
