import { json } from '@sveltejs/kit';
import { connectToDatabase } from '$lib/mongodb';
import { ObjectId } from 'mongodb';
import { generateSecretKey, generateVerificationCode } from '$lib/utils/secretKey';

// GET - List all registration requests
export async function GET({ url }: { url: URL }) {
	try {
		const db = await connectToDatabase();
		const registrationRequestsCollection = db.collection('registration_requests');

		// Get query parameters
		const status = url.searchParams.get('status') || 'all';
		const limit = parseInt(url.searchParams.get('limit') || '50');
		const skip = parseInt(url.searchParams.get('skip') || '0');

		// Build query
		let query = {};
		if (status !== 'all') {
			query = { status: status };
		}

		// Get requests with pagination
		const requests = await registrationRequestsCollection
			.find(query)
			.sort({ requestedAt: -1 })
			.limit(limit)
			.skip(skip)
			.toArray();

		// Get total count
		const totalCount = await registrationRequestsCollection.countDocuments(query);

		return json({
			success: true,
			requests: requests,
			pagination: {
				total: totalCount,
				limit: limit,
				skip: skip,
				hasMore: skip + limit < totalCount
			}
		});

	} catch (error) {
		console.error('Error fetching registration requests:', error);
		return new Response('Internal server error', { status: 500 });
	}
}

// POST - Approve a registration request and create user
export async function POST({ request }: { request: any }) {
	const { requestId, secretKey, action } = await request.json();

	if (!requestId || !action) {
		return new Response('Missing required fields', { status: 400 });
	}

	if (action === 'approve' && !secretKey) {
		return new Response('Secret key is required for approval', { status: 400 });
	}

	try {
		const db = await connectToDatabase();
		const registrationRequestsCollection = db.collection('registration_requests');
		const usersCollection = db.collection('users');

		// Find the registration request
		const registrationRequest = await registrationRequestsCollection.findOne({
			_id: new ObjectId(requestId)
		});

		if (!registrationRequest) {
			return new Response('Registration request not found', { status: 404 });
		}

		if (registrationRequest.status !== 'pending') {
			return new Response('Registration request already processed', { status: 400 });
		}

		if (action === 'approve') {
			// Generate email verification code and secret key
			const verificationCode = generateVerificationCode();
			const secretKey = generateSecretKey();

			// Create user account
			const newUser = {
				email: registrationRequest.email,
				secretKey: secretKey,
				country_code: registrationRequest.country_code,
				registrationIP: registrationRequest.requestIP,
				createdAt: new Date(),
				lastLoginAt: null,
				lastLoginIP: null,
				approvedFrom: requestId,
				userType: 'user',
				isEnabled: true,
				emailVerified: false,
				verificationCode: verificationCode
			};

			await usersCollection.insertOne(newUser);

			// Update registration request
			await registrationRequestsCollection.updateOne(
				{ _id: registrationRequest._id },
				{
					$set: {
						status: 'approved',
						secretKey: secretKey,
						approvedAt: new Date(),
						approvedBy: 'admin' // You can enhance this with actual admin user
					}
				}
			);

			return json({
				success: true,
				message: 'Registration request approved and user created',
				user: {
					email: registrationRequest.email,
					secretKey: secretKey,
					verificationCode: verificationCode
				}
			});

		} else if (action === 'generate_verification') {
			// Generate new verification code for existing user
			const verificationCode = generateVerificationCode();

			// Update user with new verification code
			const usersCollection = db.collection('users');
			await usersCollection.updateOne(
				{ email: registrationRequest.email },
				{
					$set: {
						verificationCode: verificationCode,
						emailVerified: false // Reset verification status
					}
				}
			);

			return json({
				success: true,
				message: 'Verification code generated',
				verificationCode: verificationCode,
				email: registrationRequest.email
			});

		} else if (action === 'reject') {
			// Update registration request
			await registrationRequestsCollection.updateOne(
				{ _id: registrationRequest._id },
				{
					$set: {
						status: 'rejected',
						rejectedAt: new Date(),
						rejectedBy: 'admin'
					}
				}
			);

			return json({
				success: true,
				message: 'Registration request rejected'
			});

		} else if (action === 'reconsider') {
			// Move rejected request back to pending status
			await registrationRequestsCollection.updateOne(
				{ _id: registrationRequest._id },
				{
					$set: {
						status: 'pending'
					},
					$unset: {
						rejectedAt: "",
						rejectedBy: ""
					}
				}
			);

			return json({
				success: true,
				message: 'Registration request moved back to pending'
			});

		} else if (action === 'delete') {
			// Permanently delete the rejected request
			const deleteResult = await registrationRequestsCollection.deleteOne(
				{ _id: registrationRequest._id }
			);

			if (deleteResult.deletedCount === 0) {
				return new Response('Request not found', { status: 404 });
			}

			return json({
				success: true,
				message: 'Rejected request permanently deleted'
			});
		}

		return new Response('Invalid action', { status: 400 });

	} catch (error) {
		console.error('Error processing registration request:', error);
		return new Response('Internal server error', { status: 500 });
	}
}
