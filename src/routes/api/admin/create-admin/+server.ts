import { json } from '@sveltejs/kit';
import { connectToDatabase } from '$lib/mongodb';
import { ADMIN_EMAIL } from '$env/static/private';

// POST - Create admin user (one-time setup)
export async function POST({ request }: { request: Request }) {
	const { secretKey } = await request.json();

	if (!secretKey) {
		return new Response('Secret key is required', { status: 400 });
	}

	try {
		const db = await connectToDatabase();
		const usersCollection = db.collection('users');

		// Check if admin already exists
		const existingAdmin = await usersCollection.findOne({ email: ADMIN_EMAIL });
		if (existingAdmin) {
			return new Response('Admin user already exists', { status: 409 });
		}

		// Create admin user
		const adminUser = {
			email: ADMIN_EMAIL,
			secretKey: secretKey,
			country_code: 'US', // Default country for admin
			registrationIP: '127.0.0.1',
			createdAt: new Date(),
			lastLoginAt: null,
			lastLoginIP: null,
			userType: 'admin',
			isEnabled: true,
			isAdmin: true
		};

		const result = await usersCollection.insertOne(adminUser);

		return json({
			success: true,
			message: 'Admin user created successfully',
			adminId: result.insertedId,
			email: ADMIN_EMAIL
		});

	} catch (error) {
		console.error('Error creating admin user:', error);
		return new Response('Internal server error', { status: 500 });
	}
}
