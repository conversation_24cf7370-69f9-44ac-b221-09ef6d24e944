import { json } from '@sveltejs/kit';
import { connectToDatabase } from '$lib/mongodb';

export async function POST({ request }: { request: any }) {
	const { code } = await request.json();

	if (!code) {
		return new Response('Verification code is required', { status: 400 });
	}

	try {
		const db = await connectToDatabase();
		const usersCollection = db.collection('users');

		// Find user with matching verification code
		const user = await usersCollection.findOne({ 
			verificationCode: code,
			emailVerified: false // Only allow verification if not already verified
		});

		if (!user) {
			return json({
				success: false,
				message: 'Invalid or expired verification code. Please request a new verification email.'
			}, { status: 400 });
		}

		// Update user as verified and remove verification code
		await usersCollection.updateOne(
			{ _id: user._id },
			{
				$set: {
					emailVerified: true,
					verifiedAt: new Date()
				},
				$unset: {
					verificationCode: "" // Remove the code after successful verification
				}
			}
		);

		return json({
			success: true,
			message: 'Email verified successfully',
			email: user.email
		});

	} catch (error) {
		console.error('Error verifying email:', error);
		return new Response('Internal server error', { status: 500 });
	}
}
