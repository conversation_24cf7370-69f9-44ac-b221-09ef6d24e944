import { json } from '@sveltejs/kit';
import { connectToDatabase } from '$lib/mongodb';

export async function POST({ request }: { request: any }) {
	const { email, country_code } = await request.json();

	// Validate input
	if (!email || !country_code) {
		return new Response('Missing required fields', { status: 400 });
	}

	// Basic email validation
	const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
	if (!emailRegex.test(email)) {
		return new Response('Invalid email format', { status: 400 });
	}

	// Get user IP address
	let userIP =
		request.headers.get('x-forwarded-for')?.split(',')[0]?.trim() ||
		request.headers.get('cf-connecting-ip') ||
		request.headers.get('x-real-ip') ||
		(request as any).ip || // Some frameworks attach ip directly
		'127.0.0.1';

	try {
		// Connect to MongoDB
		const db = await connectToDatabase();
		const registrationRequestsCollection = db.collection('registration_requests');

		// Check if request already exists
		const existingRequest = await registrationRequestsCollection.findOne({ email: email });
		if (existingRequest) {
			return new Response('Registration request already exists for this email', { status: 409 });
		}

		// Create new registration request
		const registrationRequest = {
			email: email,
			country_code: country_code,
			requestIP: userIP,
			requestedAt: new Date(),
			status: 'pending', // pending, approved, rejected
			secretKey: null, // Will be filled when approved
			approvedAt: null,
			approvedBy: null
		};

		const result = await registrationRequestsCollection.insertOne(registrationRequest);

		// Return success response
		return json({
			success: true,
			message: 'Registration request submitted successfully. We will send you a secret key via email.',
			requestId: result.insertedId
		});

	} catch (error) {
		console.error('Registration request error:', error);
		return new Response('Internal server error', { status: 500 });
	}
}
