import { json } from '@sveltejs/kit';
import { connectToDatabase } from '$lib/mongodb';
import { ADMIN_EMAIL } from '$env/static/private';

export async function POST({ request }: { request: any }) {
	const { email, secretKey, country_code } = await request.json();

	// Validate input
	if (!email || !secretKey || !country_code) {
		return new Response('Missing required fields', { status: 400 });
	}

	// Get user IP address
	let userIP =
		request.headers.get('x-forwarded-for')?.split(',')[0]?.trim() ||
		request.headers.get('cf-connecting-ip') ||
		request.headers.get('x-real-ip') ||
		(request as any).ip || // Some frameworks attach ip directly
		'127.0.0.1';

	try {
		// Connect to MongoDB
		const db = await connectToDatabase();
		const usersCollection = db.collection('users');

		// Find user by email and secretKey
		const user = await usersCollection.findOne({
			email: email,
			secretKey: secretKey
		});

		if (!user) {
			return new Response('Invalid email or secret key', { status: 401 });
		}

		// Check if user is enabled (default to true for existing users)
		if (user.isEnabled === false) {
			return new Response('Account is disabled. Please contact administrator.', { status: 403 });
		}

		// Check if user has verified their email (default to true for existing users without this field)
		if (user.emailVerified === false) {
			return new Response('Please verify your email address before logging in. Check your email for the verification link.', { status: 403 });
		}

		// Determine user type
		const userType = email === ADMIN_EMAIL ? 'admin' : (user.userType || 'user');
		const isAdmin = userType === 'admin';

		// Update user's country, last login info, and user type
		await usersCollection.updateOne(
			{ _id: user._id },
			{
				$set: {
					country_code: country_code,
					lastLoginIP: userIP,
					lastLoginAt: new Date(),
					userType: userType,
					isEnabled: user.isEnabled !== false // Ensure isEnabled is set
				}
			}
		);

		// Return user data (excluding sensitive information)
		const userData = {
			id: user._id,
			email: user.email,
			country_code: country_code,
			userType: userType,
			isAdmin: isAdmin,
			isEnabled: user.isEnabled !== false,
			lastLoginAt: new Date()
		};

		return json({
			success: true,
			message: 'Login successful',
			user: userData
		});

	} catch (error) {
		console.error('Login error:', error);
		return new Response('Internal server error', { status: 500 });
	}
}
