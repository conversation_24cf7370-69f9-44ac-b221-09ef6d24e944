import { MongoClient } from 'mongodb';
import { MONGODB_URI_KAMATERA } from '$env/static/private';

let client: MongoClient | null = null;

export async function connectToDatabase() {
	if (!client) {
		client = new MongoClient(MONGODB_URI_KAMATERA);
		await client.connect();
	}
	return client.db('cinemated');
}

export async function closeDatabaseConnection() {
	if (client) {
		await client.close();
		client = null;
	}
}
