<script lang="ts">
    import LoadingIndicator from './Loading.svelte';

    export let loading: boolean;
    export let recommendations: Array<any> = [];
    export let search: () => void;
    export let clearForm: () => void;
</script>

<div class="mt-16">
    <button
        type="button"
        on:click={search}
        class={`btn-get-recommendations ${
            loading
                ? 'bg-turquoise-400/50'
                : 'bg-turquoise-600 hover:bg-gradient-to-r from-turquoise-700 via-turquoise-600 to-turquoise-700 '
        } mt-4 w-full h-10 text-white font-bold p-3 rounded-full flex items-center justify-center`}>
        {#if loading}
            <LoadingIndicator />
        {:else}
            {#if recommendations.length > 0}
                <p>Show me more recommendations</p>
            {:else}
                <p>Show me recommendations</p>
            {/if}
        {/if}
    </button>
    {#if recommendations.length > 0}
        <button
            type="button"
            on:click={clearForm}
            class="btn-clear-search bg-white/20 hover:bg-white/30 mt-4 w-full h-10 text-white font-bold p-3 rounded-full flex items-center justify-center">
            Clear Search
        </button>
    {/if}
</div>