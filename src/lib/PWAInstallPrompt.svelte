<script lang="ts">
	import { onMount } from 'svelte';
	import { browser } from '$app/environment';

	let deferredPrompt: any = null;
	let showInstallPrompt = false;
	let isInstalled = false;
	let isIOS = false;
	let isStandalone = false;
	let isDismissed = false;
	let isSafari = false;
	let browserName = '';

	onMount(() => {
		if (!browser) return;

		// Check if app is already installed
		isStandalone = window.matchMedia('(display-mode: standalone)').matches;
		isInstalled = isStandalone || (window.navigator as any).standalone;

		// Detect iOS and browser
		isIOS = /iPad|iPhone|iPod/.test(navigator.userAgent);
		isSafari = /Safari/.test(navigator.userAgent) && !/Chrome|CriOS|FxiOS|EdgiOS/.test(navigator.userAgent);

		// Detect specific browsers
		if (/brave/i.test(navigator.userAgent) || (navigator as any).brave) {
			browserName = 'Brave';
		} else if (/CriOS/.test(navigator.userAgent)) {
			browserName = 'Chrome';
		} else if (/FxiOS/.test(navigator.userAgent)) {
			browserName = 'Firefox';
		} else if (isSafari) {
			browserName = 'Safari';
		} else {
			browserName = 'Browser';
		}

		// Listen for the beforeinstallprompt event
		window.addEventListener('beforeinstallprompt', (e) => {
			e.preventDefault();
			deferredPrompt = e;
			showInstallPrompt = true;
		});

		// Listen for app installed event
		window.addEventListener('appinstalled', () => {
			showInstallPrompt = false;
			isInstalled = true;
			deferredPrompt = null;
		});

		// Check if app is already installed on iOS
		if (isIOS && isStandalone) {
			isInstalled = true;
		}
	});

	async function handleInstall() {
		if (!deferredPrompt) return;

		deferredPrompt.prompt();
		const { outcome } = await deferredPrompt.userChoice;
		
		if (outcome === 'accepted') {
			showInstallPrompt = false;
		}
		
		deferredPrompt = null;
	}

	function dismissPrompt() {
		showInstallPrompt = false;
		isDismissed = true;
		// Remember user dismissed the prompt
		if (browser) {
			localStorage.setItem('pwa-install-dismissed', 'true');
		}
	}

	// Check if user previously dismissed the prompt and initialize state
	onMount(() => {
		if (!browser) return;

		// Check if user previously dismissed the prompt
		const dismissed = localStorage.getItem('pwa-install-dismissed');
		if (dismissed) {
			isDismissed = true;
			showInstallPrompt = false;
			return;
		}

		// For iOS devices, show the prompt if not installed and not dismissed
		// Show for all iOS browsers but with different messages
		if (isIOS && !isInstalled && !isDismissed) {
			showInstallPrompt = true;
		}
	});
</script>

{#if browser && !isInstalled && showInstallPrompt && !isDismissed}
	<div class="fixed bottom-4 left-4 right-4 z-50 md:left-auto md:right-4 md:max-w-sm">
		<div class="bg-slate-800/95 backdrop-blur-sm border border-turquoise-500/20 rounded-lg p-4 shadow-xl">
			<div class="flex items-start gap-3">
				<div class="flex-shrink-0">
					<img src="/icon-72x72.png" alt="Cinemated" class="w-12 h-12 rounded-lg" />
				</div>
				<div class="flex-1 min-w-0">
					<h3 class="text-white font-semibold text-sm mb-1">Install Cinemated</h3>
					{#if isIOS}
						{#if isSafari}
							<p class="text-white/70 text-xs mb-3">
								Tap the share button <svg class="inline w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
									<path d="M15 8a3 3 0 10-2.977-2.63l-4.94 2.47a3 3 0 100 4.319l4.94 2.47a3 3 0 10.895-1.789l-4.94-2.47a3.027 3.027 0 000-.74l4.94-2.47C13.456 7.68 14.19 8 15 8z"/>
								</svg> then "Add to Home Screen"
							</p>
						{:else}
							<p class="text-white/70 text-xs mb-3">
								⚠️ For best PWA experience on iOS, please open this app in <strong>Safari</strong> browser. {browserName} has limited PWA support.
							</p>
						{/if}
					{:else}
						<p class="text-white/70 text-xs mb-3">
							Get quick access and offline features by installing our app
						</p>
					{/if}
					<div class="flex gap-2">
						{#if !isIOS}
							<button
								on:click={handleInstall}
								class="bg-turquoise-500 hover:bg-turquoise-600 text-slate-900 px-3 py-1.5 rounded text-xs font-medium transition-colors"
							>
								Install
							</button>
						{/if}
						<button
							on:click={dismissPrompt}
							class="text-white/70 hover:text-white px-3 py-1.5 rounded text-xs transition-colors"
						>
							Dismiss
						</button>
					</div>
				</div>
				<button
					on:click={dismissPrompt}
					class="flex-shrink-0 text-white/50 hover:text-white transition-colors"
				>
					<svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
						<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
					</svg>
				</button>
			</div>
		</div>
	</div>
{/if}
