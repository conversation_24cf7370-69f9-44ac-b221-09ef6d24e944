/**
 * Centralized secret key generation utility
 * Ensures consistent secret key format across the entire application
 */

/**
 * Generates a secure random secret key for user authentication
 * Format: Two random alphanumeric strings concatenated (no prefix)
 * Length: ~26 characters
 * @returns {string} A secure random secret key
 */
export function generateSecretKey(): string {
	return Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);
}

/**
 * Generates a secure random verification code for email verification
 * Format: Two random alphanumeric strings concatenated
 * Length: ~26 characters
 * @returns {string} A secure random verification code
 */
export function generateVerificationCode(): string {
	return Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);
}
