export interface SpecialGenre {
    genre_id: string;
    name: string;
    name_slug: string;
    description: string;
    prompt1: string;
    prompt2: string;
    prompt3: string;
    tvprompt1: string;
    tvprompt2: string;
    tvprompt3: string;
    bg_image: string;
    bg_gif: string;
    is_visible: string;
}

export const specialGenres: SpecialGenre[] = [
    {
        genre_id: '1',
        name: 'Charming Detective',
        name_slug: 'charming-detective',
        description: 'Uncover the secrets of intricate puzzles and immerse yourself in a world of suspense as a charismatic detective uses their wit and charm to solve baffling mysteries.',
        prompt1: `{{movieType}} Recommendations: Charming Detectives

Core concept: A charismatic, witty detective solves a baffling mystery using intellect and charm.
Keywords: whodunnit, intricate puzzle, suspense, sophisticated, clever, stylish.
Examples: Knives <PERSON>, <PERSON> (2009), Kiss Kiss Bang Bang.
Avoid: Gritty noir (Chinatown), bleak thrillers (Se7en).

List 5 {{movieType}} that fit.`,
        prompt2: `Generate {{movieType}} recommendations based on the following genre recipe.

**Genre Profile: The Charismatic Sleuth**

**Protagonist Formula:**
* **Base:** 1 part Brilliant Intellect
* **Primary Flavors:** 2 parts Sharp Wit & Suave Charm
* **Garnish:** A dash of Eccentricity

**Conflict Formula:**
* **Core:** 1 part Baffling, Intricate Mystery
* **Mix-ins:** Red Herrings, Clever Twists
* **Serve with:** High Stakes & Suspense

**Tonal Profile:**
* **Include:** Stylish, Sophisticated, Clever, Engaging
* **Exclude:** Gritty, Bleak, Gory, Hard-Boiled Noir

**Touchstone Characters (for calibration):**
* Benoit Blanc (Knives Out)
* Sherlock Holmes (Robert Downey Jr. version)
* Nick Charles (The Thin Man)

**Your Task:**
List 5 {{movieType}} that perfectly match this recipe. For each, briefly explain why its detective and plot are a match.`,
        prompt3: `Find {{movieType}} matching the following tag profile.

**//-- MUST INCLUDE --//**
+detective +mystery +charismatic_protagonist +witty_dialogue +intricate_plot +suspense

**//-- SHOULD INCLUDE --//**
+whodunnit +stylish +sophisticated +clever +comedy_undertones

**//-- MUST NOT INCLUDE --//**
-noir -hard_boiled -bleak -graphic_violence -police_procedural

**//-- EXAMPLES --//**
+Knives Out (2019)
+Glass Onion (2022)
+The Nice Guys (2016)

**//-- OUTPUT --//**
Return a list of 5 {{movieType}} with a 1-sentence justification for each.`,
        tvprompt1: `{{movieType}} Recommendations: Charming Detectives

Core concept: A charismatic, witty detective solves a baffling mystery using intellect and charm.
Keywords: whodunnit, intricate puzzle, suspense, sophisticated, clever, stylish.
Examples: Poker Face, Only Murders in the Building, Monk, Castle.
Avoid: Gritty police procedurals (The Wire), bleak crime dramas (True Detective).

List 5 {{movieType}} that fit.`,
        tvprompt2: `Generate {{movieType}} recommendations based on the following genre recipe.

**Genre Profile: The Charismatic Sleuth**

**Protagonist Formula:**
* **Base:** 1 part Brilliant Intellect
* **Primary Flavors:** 2 parts Sharp Wit & Suave Charm
* **Garnish:** A dash of Eccentricity

**Conflict Formula:**
* **Core:** A new Baffling Mystery each episode or season.
* **Mix-ins:** Red Herrings, Clever Twists
* **Serve with:** High Stakes & Suspense

**Tonal Profile:**
* **Include:** Stylish, Sophisticated, Clever, Engaging
* **Exclude:** Gritty, Bleak, Gory, Hard-Boiled Noir

**Touchstone Characters (for calibration):**
* Charlie Cale (Poker Face)
* Adrian Monk (Monk)
* Patrick Jane (The Mentalist)

**Your Task:**
List 5 {{movieType}} that perfectly match this recipe. For each, briefly explain why its detective and plot are a match.`,
        tvprompt3: `Find {{movieType}} matching the following tag profile.

**//-- MUST INCLUDE --//**
+detective +mystery +charismatic_protagonist +witty_dialogue +case_of_the_week +suspense

**//-- SHOULD INCLUDE --//**
+whodunnit +stylish +sophisticated +clever +comedy_undertones

**//-- MUST NOT INCLUDE --//**
-noir -hard_boiled -bleak -graphic_violence -police_procedural

**//-- EXAMPLES --//**
+Poker Face (2023)
+Psych (2006)
+Only Murders in the Building (2021)

**//-- OUTPUT --//**
Return a list of 5 {{movieType}} with a 1-sentence justification for each.`,
        bg_image: 'charming_detective.jpg',
        bg_gif: 'charming_detective.gif',
        is_visible: '1'
    },
    {
        genre_id: '2',
        name: 'Catastrophe Chronicles',
        name_slug: 'catastrophe-chronicles',
        description: 'Brace yourself for heart-pounding action and gripping intensity as cataclysmic events push the limits of human survival, testing courage and resilience in the face of overwhelming chaos.',
        prompt1: `{{movieType}} Recommendations: Catastrophe Chronicles

Core concept: Humans struggle for survival against a large-scale, cataclysmic event.
Keywords: disaster, survival, action, chaos, visual effects, natural disaster.
Examples: 2012, The Day After Tomorrow, The Impossible.
Avoid: Monster movies (Godzilla), alien invasions (Independence Day).

List 5 {{movieType}} that fit.`,
        prompt2: `Generate {{movieType}} recommendations based on the following genre recipe.

**Genre Profile: The Global Survival Thriller**

**Event Formula:**
* **Base:** 1 part Overwhelming Cataclysm (Natural, Climate, Geological)
* **Impact:** Global or massive regional scale
* **Garnish:** Spectacular special effects

**Character Formula:**
* **Core:** Ordinary people, scientists, or estranged families forced to be heroes.
* **Goal:** Survival, rescue, reaching a safe zone.

**Tonal Profile:**
* **Include:** Intense, thrilling, high-stakes, action-packed, spectacular.
* **Exclude:** Slow-paced, low-stakes, monster-focused.

**Touchstone Events (for calibration):**
* The superstorm in The Day After Tomorrow
* The mega-earthquake in San Andreas
* The tsunami in The Impossible

**Your Task:**
List 5 {{movieType}} that perfectly match this recipe. For each, briefly state the cataclysmic event.`,
        prompt3: `Find {{movieType}} matching the following tag profile.

**//-- MUST INCLUDE --//**
+disaster +survival +action +large_scale +visual_effects +chaos

**//-- SHOULD INCLUDE --//**
+natural_disaster +end_of_world +intense

**//-- MUST NOT INCLUDE --//**
-monster_movie -zombie -alien_invasion -small_scale

**//-- EXAMPLES --//**
+2012 (2009)
+Twister (1996)
+Greenland (2020)

**//-- OUTPUT --//**
Return a list of 5 {{movieType}} with a 1-sentence justification for each.`,
        tvprompt1: `{{movieType}} Recommendations: Catastrophe Chronicles

Core concept: Characters navigate the aftermath of a society-altering cataclysm, focusing on survival and resilience.
Keywords: post-apocalyptic, survival, disaster, societal collapse, human drama.
Examples: The Last of Us, Chernobyl, Station Eleven.
Avoid: Sitcoms, case-of-the-week procedurals.

List 5 {{movieType}} that fit.`,
        tvprompt2: `Generate {{movieType}} recommendations based on the following genre recipe.

**Genre Profile: The Post-Cataclysm Saga**

**Event Formula:**
* **Base:** The world after a devastating event (pandemic, nuclear meltdown, natural disaster).
* **Focus:** Long-term survival and rebuilding.

**Character Formula:**
* **Core:** Survivors grappling with loss, forming new communities, and facing new dangers.
* **Goal:** Find hope, safety, and meaning in a broken world.

**Tonal Profile:**
* **Include:** Intense, gripping, dramatic, character-driven, thrilling.
* **Exclude:** Light-hearted, comedic, monster-of-the-week.

**Touchstone Shows (for calibration):**
* Chernobyl (2019)
* The Last of Us (2023)
* Silo (2023)

**Your Task:**
List 5 {{movieType}} that perfectly match this recipe. For each, briefly state the nature of the catastrophe.`,
        tvprompt3: `Find {{movieType}} matching the following tag profile.

**//-- MUST INCLUDE --//**
+disaster +survival +post_apocalyptic +drama +societal_collapse

**//-- SHOULD INCLUDE --//**
+intense +character_driven +thriller

**//-- MUST NOT INCLUDE --//**
-comedy -superhero -monster_of_the_week

**//-- EXAMPLES --//**
+Station Eleven (2021)
+The Walking Dead (2010)
+From (2022)

**//-- OUTPUT --//**
Return a list of 5 {{movieType}} with a 1-sentence justification for each.`,
        bg_image: 'disaster_movies.jpg',
        bg_gif: 'disaster_movies.gif',
        is_visible: '1'
    },
    {
        genre_id: '3',
        name: 'Gore Fest',
        name_slug: 'gore-fest',
        description: 'Dive into the darkest depths of terror as these bone-chilling horror movies unleash a visceral and graphic display of gore, delivering spine-tingling thrills and unrelenting suspense.',
        prompt1: `{{movieType}} Recommendations: Gore Fest

Core concept: Horror that prioritizes explicit, graphic gore and visceral, shocking violence.
Keywords: gore, splatter, body horror, slasher, graphic, visceral.
Examples: Saw, Hostel, Evil Dead (2013).
Avoid: Psychological thrillers, supernatural ghost stories with no gore.

List 5 {{movieType}} that fit.`,
        prompt2: `Generate {{movieType}} recommendations based on the following genre recipe.

**Genre Profile: The Splatter-Fest**

**Horror Formula:**
* **Base:** 2 parts Visceral, Graphic Gore
* **Mix-ins:** Creative Kills, Body Horror, Practical Effects
* **Serve with:** Unrelenting Terror & Shock Value

**Tonal Profile:**
* **Include:** Stomach-churning, shocking, intense, brutal.
* **Exclude:** Subtle, suggestive, atmospheric, psychological.

**Touchstone Films (for calibration):**
* Terrifier (2016)
* Braindead / Dead Alive (1992)
* High Tension (2003)

**Your Task:**
List 5 {{movieType}} that perfectly match this recipe. For each, briefly explain why it's a prime example of a gore fest.`,
        prompt3: `Find {{movieType}} matching the following tag profile.

**//-- MUST INCLUDE --//**
+horror +gore +graphic_violence +splatter +body_horror +shocking

**//-- SHOULD INCLUDE --//**
+slasher +torture +practical_effects

**//-- MUST NOT INCLUDE --//**
-psychological_horror -ghost_story -found_footage -subtle

**//-- EXAMPLES --//**
+The Sadness (2021)
+Martyrs (2008)
+Inside (2007)

**//-- OUTPUT --//**
Return a list of 5 {{movieType}} with a 1-sentence justification for each.`,
        tvprompt1: `{{movieType}} Recommendations: Gore Fest

Core concept: Horror series defined by their relentless and graphic displays of gore, violence, and splatter.
Keywords: gore, horror, splatter, graphic violence, body horror.
Examples: The Boys, Invincible, Ash vs Evil Dead.
Avoid: Psychological thrillers, subtle supernatural horror.

List 5 {{movieType}} that fit.`,
        tvprompt2: `Generate {{movieType}} recommendations based on the following genre recipe.

**Genre Profile: The Splatter Series**

**Horror Formula:**
* **Base:** 2 parts Visceral, Graphic Gore over multiple episodes.
* **Mix-ins:** Creative Kills, Body Horror, Dark Humor.
* **Serve with:** Unrelenting Action & Shock Value

**Tonal Profile:**
* **Include:** Stomach-churning, shocking, intense, brutal, often satirical.
* **Exclude:** Subtle, suggestive, purely atmospheric.

**Touchstone Shows (for calibration):**
* The Boys (2019)
* Hannibal (2013)
* American Horror Story (specifically seasons like 'Roanoke' or '1984')

**Your Task:**
List 5 {{movieType}} that perfectly match this recipe. For each, briefly explain its approach to gore.`,
        tvprompt3: `Find {{movieType}} matching the following tag profile.

**//-- MUST INCLUDE --//**
+horror +gore +graphic_violence +splatter +body_horror +shocking

**//-- SHOULD INCLUDE --//**
+action +dark_humor +supernatural

**//-- MUST NOT INCLUDE --//**
-psychological_thriller -ghost_story -subtle

**//-- EXAMPLES --//**
+Invincible (2021)
+The Walking Dead (2010)
+Ash vs Evil Dead (2015)

**//-- OUTPUT --//**
Return a list of 5 {{movieType}} with a 1-sentence justification for each.`,
        bg_image: 'gore_fest.jpg',
        bg_gif: 'gore_fest.gif',
        is_visible: '1'
    },
    {
        genre_id: '4',
        name: 'Scary Castles',
        name_slug: 'scary-castles',
        description: 'Experience a chilling journey through shadowy corridors and secret chambers as horror movies set in eerie castles bring forth the sinister and supernatural, leaving you gripped with fear.',
        prompt1: `{{movieType}} Recommendations: Scary Castles

Core concept: Gothic or supernatural horror set within an eerie castle, ancient manor, or abbey.
Keywords: gothic, haunted, castle, supernatural, eerie, atmospheric.
Examples: The Others, Crimson Peak, The Woman in Black.
Avoid: Modern slashers, sci-fi horror.

List 5 {{movieType}} that fit.`,
        prompt2: `Generate {{movieType}} recommendations based on the following genre recipe.

**Genre Profile: Gothic Castle Horror**

**Setting Formula:**
* **Base:** 1 part Isolated, Decaying Castle or Manor
* **Mix-ins:** Secret Passages, Shadowy Corridors, Family Graveyard

**Conflict Formula:**
* **Core:** A Sinister Presence, a Vengeful Ghost, or a Dark Family Secret.
* **Goal:** Survive the night, uncover the truth, escape the curse.

**Tonal Profile:**
* **Include:** Atmospheric, suspenseful, dread-filled, chilling, gothic.
* **Exclude:** Action-heavy, comedic, slasher-focused.

**Touchstone Films (for calibration):**
* The Haunting (1963)
* Bram Stoker's Dracula (1992)
* The Others (2001)

**Your Task:**
List 5 {{movieType}} that perfectly match this recipe. Briefly state the castle/manor at the center of the horror.`,
        prompt3: `Find {{movieType}} matching the following tag profile.

**//-- MUST INCLUDE --//**
+horror +castle +gothic +haunted +supernatural +atmospheric

**//-- SHOULD INCLUDE --//**
+ghost +vampire +mystery +period_setting

**//-- MUST NOT INCLUDE --//**
-slasher -zombie -sci_fi -modern_suburbia

**//-- EXAMPLES --//**
+Crimson Peak (2015)
+The Name of the Rose (1986)
+The Orphanage (2007)

**//-- OUTPUT --//**
Return a list of 5 {{movieType}} with a 1-sentence justification for each.`,
        tvprompt1: `{{movieType}} Recommendations: Scary Castles & Manors

Core concept: Gothic or supernatural horror series centered on an eerie and atmospheric castle, mansion, or estate.
Keywords: gothic horror, haunted house, supernatural, dread, family secrets.
Examples: The Haunting of Hill House, The Haunting of Bly Manor, Midnight Mass.
Avoid: Slasher series, sci-fi horror.

List 5 {{movieType}} that fit.`,
        tvprompt2: `Generate {{movieType}} recommendations based on the following genre recipe.

**Genre Profile: Gothic Manor Horror**

**Setting Formula:**
* **Base:** 1 Isolated, Decaying Manor or Estate that feels like a character itself.
* **Mix-ins:** Forbidden rooms, ghostly apparitions, tragic history.

**Conflict Formula:**
* **Core:** A family haunted by the house, its history, and their own trauma.
* **Goal:** Confront the past, save the family, escape the influence of the house.

**Tonal Profile:**
* **Include:** Atmospheric, dread-filled, emotional, character-driven, supernatural.
* **Exclude:** Action-heavy, comedic, monster-of-the-week.

**Touchstone Shows (for calibration):**
* The Haunting of Hill House (2018)
* The Haunting of Bly Manor (2020)
* Penny Dreadful (2014)

**Your Task:**
List 5 {{movieType}} that perfectly match this recipe. Name the central haunted location in each series.`,
        tvprompt3: `Find {{movieType}} matching the following tag profile.

**//-- MUST INCLUDE --//**
+horror +gothic +haunted_house +supernatural +atmospheric +drama

**//-- SHOULD INCLUDE --//**
+ghost +family_secrets +mystery +dread

**//-- MUST NOT INCLUDE --//**
-slasher -zombie -sci_fi -light_hearted

**//-- EXAMPLES --//**
+Midnight Mass (2021)
+The Fall of the House of Usher (2023)
+Marianne (2019)

**//-- OUTPUT --//**
Return a list of 5 {{movieType}} with a 1-sentence justification for each.`,
        bg_image: 'scary_castles.jpg',
        bg_gif: 'scary_castles.gif',
        is_visible: '1'
    },
    {
        genre_id: '5',
        name: 'Larger than Life',
        name_slug: 'larger-than-life',
        description: 'Immerse yourself in a world of epic proportions where grand stories, spectacular visuals, and larger-than-life characters come together to create unforgettable cinematic experiences that will leave you in awe.',
        prompt1: `{{movieType}} Recommendations: Larger than Life

Core concept: Epic films defined by their grand scale, spectacular visuals, and sweeping stories.
Keywords: epic, spectacle, blockbuster, grand, awe-inspiring, historical, fantasy.
Examples: The Lord of the Rings, Gladiator, Lawrence of Arabia.
Avoid: Small-scale indie dramas, quiet character studies.

List 5 {{movieType}} that fit.`,
        prompt2: `Generate {{movieType}} recommendations based on the following genre recipe.

**Genre Profile: The Cinematic Epic**

**Story Formula:**
* **Base:** 1 part Sweeping, Grand Narrative (history, war, fantasy, sci-fi)
* **Scale:** Spans years or entire worlds.

**Character Formula:**
* **Core:** Iconic, larger-than-life heroes on a world-changing journey.

**Production Formula:**
* **Key Ingredients:** Awe-inspiring cinematography, massive scope, lavish production design.

**Tonal Profile:**
* **Include:** Grandiose, awe-inspiring, unforgettable, spectacular.
* **Exclude:** Mundane, understated, minimalist, claustrophobic.

**Touchstone Films (for calibration):**
* Ben-Hur (1959)
* Dune (2021)
* Avatar (2009)

**Your Task:**
List 5 {{movieType}} that perfectly match this recipe. Briefly explain what makes them 'larger-than-life'.`,
        prompt3: `Find {{movieType}} matching the following tag profile.

**//-- MUST INCLUDE --//**
+epic +spectacle +adventure +large_scale +stunning_visuals +blockbuster

**//-- SHOULD INCLUDE --//**
+historical +fantasy +war +biopic

**//-- MUST NOT INCLUDE --//**
-indie -slice_of_life -mumblecore -single_location

**//-- EXAMPLES --//**
+The Lord of the Rings: The Return of the King (2003)
+Ran (1985)
+Mad Max: Fury Road (2015)

**//-- OUTPUT --//**
Return a list of 5 {{movieType}} with a 1-sentence justification for each.`,
        tvprompt1: `{{movieType}} Recommendations: Larger than Life

Core concept: Epic TV series with huge budgets, spectacular visuals, complex world-building, and sweeping, multi-season stories.
Keywords: epic, fantasy, historical drama, sci-fi, world-building, spectacle.
Examples: Game of Thrones, The Rings of Power, Foundation.
Avoid: Sitcoms, police procedurals, small-scale dramas.

List 5 {{movieType}} that fit.`,
        tvprompt2: `Generate {{movieType}} recommendations based on the following genre recipe.

**Genre Profile: The Epic Series**

**Story Formula:**
* **Base:** A sweeping, multi-season narrative (history, fantasy, sci-fi).
* **Scale:** Spans entire worlds, dynasties, or generations.

**Production Formula:**
* **Core:** Massive scope, lavish production design, high-end visual effects.
* **Result:** A truly cinematic experience on the small screen.

**Tonal Profile:**
* **Include:** Grandiose, awe-inspiring, complex, immersive.
* **Exclude:** Mundane, understated, minimalist, 'bottle episodes'.

**Touchstone Shows (for calibration):**
* Game of Thrones (2011)
* The Crown (2016)
* House of the Dragon (2022)

**Your Task:**
List 5 {{movieType}} that perfectly match this recipe. Briefly explain the source of their epic scale.`,
        tvprompt3: `Find {{movieType}} matching the following tag profile.

**//-- MUST INCLUDE --//**
+epic +spectacle +world_building +large_scale +stunning_visuals +drama

**//-- SHOULD INCLUDE --//**
+historical +fantasy +sci_fi +political_intrigue

**//-- MUST NOT INCLUDE --//**
-sitcom -procedural -monster_of_the_week

**//-- EXAMPLES --//**
+Foundation (2021)
+The Lord of the Rings: The Rings of Power (2022)
+Westworld (2016)

**//-- OUTPUT --//**
Return a list of 5 {{movieType}} with a 1-sentence justification for each.`,
        bg_image: 'larger_than_life.jpg',
        bg_gif: 'larger_than_life.gif',
        is_visible: '1'
    },
    {
        genre_id: '6',
        name: 'Space Adventures',
        name_slug: 'space-adventures',
        description: 'Embark on a mesmerizing voyage through the vastness of space as these captivating movies present awe-inspiring views of distant galaxies, celestial wonders, and the limitless possibilities of the universe.',
        prompt1: `{{movieType}} Recommendations: Space Adventures

Core concept: Awe-inspiring adventures set in space, focusing on exploration, wonder, and visual spectacle.
Keywords: space opera, sci-fi, exploration, astronaut, galaxy, spaceship.
Examples: Interstellar, Gravity, The Martian.
Avoid: Sci-fi set entirely on Earth, dystopian futures without space travel.

List 5 {{movieType}} that fit.`,
        prompt2: `Generate {{movieType}} recommendations based on the following genre recipe.

**Genre Profile: The Cosmic Voyage**

**Setting Formula:**
* **Base:** The vastness of outer space.
* **Locations:** Spaceships, alien planets, distant galaxies, space stations.

**Plot Formula:**
* **Core:** A journey of exploration, survival, first contact, or discovery.

**Tonal Profile:**
* **Include:** Awe-inspiring, wondrous, adventurous, visually stunning, suspenseful.
* **Exclude:** Earth-bound, mundane, low-tech.

**Touchstone Films (for calibration):**
* 2001: A Space Odyssey (1968)
* Star Wars: Episode IV - A New Hope (1977)
* Blade Runner 2049 (visually, for its sci-fi grandeur)

**Your Task:**
List 5 {{movieType}} that perfectly match this recipe. Briefly describe the central space voyage in each.`,
        prompt3: `Find {{movieType}} matching the following tag profile.

**//-- MUST INCLUDE --//**
+space +sci_fi +adventure +exploration +astronaut +visuals

**//-- SHOULD INCLUDE --//**
+spaceship +alien +galaxy +space_opera

**//-- MUST NOT INCLUDE --//**
-earthbound -fantasy -dystopian_only

**//-- EXAMPLES --//**
+Ad Astra (2019)
+Guardians of the Galaxy (2014)
+Arrival (2016)

**//-- OUTPUT --//**
Return a list of 5 {{movieType}} with a 1-sentence justification for each.`,
        tvprompt1: `{{movieType}} Recommendations: Space Adventures

Core concept: Sci-fi series focused on space travel, exploration of distant worlds, and life aboard a starship.
Keywords: space opera, sci-fi, exploration, starship, galaxy, aliens.
Examples: Star Trek: The Next Generation, The Expanse, Firefly.
Avoid: Sci-fi series set entirely on present-day Earth.

List 5 {{movieType}} that fit.`,
        tvprompt2: `Generate {{movieType}} recommendations based on the following genre recipe.

**Genre Profile: The Star-Trekking Saga**

**Setting Formula:**
* **Base:** The bridge of a starship or the vastness of space.
* **Locations:** Alien planets, space stations, strange new worlds.

**Plot Formula:**
* **Core:** A long-form journey of exploration, galactic conflict, or survival.
* **Structure:** Can be episodic (new world each week) or serialized (season-long arcs).

**Tonal Profile:**
* **Include:** Adventurous, wondrous, philosophical, action-packed.
* **Exclude:** Mundane, Earth-bound, non-scientific fantasy.

**Touchstone Shows (for calibration):**
* The Expanse (2015)
* Battlestar Galactica (2004)
* The Mandalorian (2019)

**Your Task:**
List 5 {{movieType}} that perfectly match this recipe. Briefly name the central starship or mission.`,
        tvprompt3: `Find {{movieType}} matching the following tag profile.

**//-- MUST INCLUDE --//**
+space +sci_fi +adventure +exploration +spaceship

**//-- SHOULD INCLUDE --//**
+space_opera +aliens +galaxy +crew_dynamics

**//-- MUST NOT INCLUDE --//**
-earthbound -fantasy -supernatural

**//-- EXAMPLES --//**
+Firefly (2002)
+Star Trek: Strange New Worlds (2022)
+For All Mankind (2019)

**//-- OUTPUT --//**
Return a list of 5 {{movieType}} with a 1-sentence justification for each.`,
        bg_image: 'space_movies.jpg',
        bg_gif: 'space_movies.gif',
        is_visible: '1'
    },
    {
        genre_id: '7',
        name: 'Spine Chillers',
        name_slug: 'spine-chillers',
        description: 'Brace yourself for a hair-raising cinematic journey into the realm of spine-chilling horror, where eerie atmospheres, heart-stopping suspense, and terrifying encounters will haunt your dreams.',
        prompt1: `{{movieType}} Recommendations: Spine Chillers

Core concept: Atmospheric horror that builds terror through suspense, dread, and psychological tension.
Keywords: psychological horror, supernatural, suspense, dread, eerie, atmospheric.
Examples: The Conjuring, Hereditary, It Follows.
Avoid: Gore-fests, splatter films, action-horror.

List 5 {{movieType}} that fit.`,
        prompt2: `Generate {{movieType}} recommendations based on the following genre recipe.

**Genre Profile: The Dread-Inducer**

**Horror Formula:**
* **Base:** 2 parts Slow-Burn Tension
* **Primary Flavor:** 1 part Psychological Fear
* **Garnish:** Eerie Atmosphere

**Antagonist Style:**
* **Core:** An unseen force, a haunting presence, a psychological breakdown, or a supernatural curse.

**Tonal Profile:**
* **Include:** Unsettling, suspenseful, atmospheric, terrifying, dread-filled.
* **Exclude:** Graphic gore, constant jump scares, horror-comedy.

**Touchstone Films (for calibration):**
* The Ring (2002)
* A Quiet Place (2018)
* The Witch (2015)

**Your Task:**
List 5 {{movieType}} that perfectly match this recipe. Briefly state the source of the dread in each film.`,
        prompt3: `Find {{movieType}} matching the following tag profile.

**//-- MUST INCLUDE --//**
+horror +suspense +psychological_horror +atmospheric +dread +supernatural

**//-- SHOULD INCLUDE --//**
+ghost +haunting +curse +slow_burn

**//-- MUST NOT INCLUDE --//**
-gore -splatter -comedy -action_horror

**//-- EXAMPLES --//**
+The Babadook (2014)
+Sinister (2012)
+Talk to Me (2023)

**//-- OUTPUT --//**
Return a list of 5 {{movieType}} with a 1-sentence justification for each.`,
        tvprompt1: `{{movieType}} Recommendations: Spine Chillers

Core concept: Horror series that build terror through sustained atmosphere, psychological dread, and suspense.
Keywords: psychological horror, supernatural, suspense, dread, slow-burn.
Examples: Severance, The Outsider, Black Mirror.
Avoid: Gore-fests, slasher series, action-horror.

List 5 {{movieType}} that fit.`,
        tvprompt2: `Generate {{movieType}} recommendations based on the following genre recipe.

**Genre Profile: The Slow-Burn Scare**

**Horror Formula:**
* **Base:** 2 parts Slow-Burn Tension over a full season.
* **Primary Flavor:** 1 part Psychological or Existential Fear.
* **Garnish:** Deeply Eerie Atmosphere.

**Tonal Profile:**
* **Include:** Unsettling, suspenseful, atmospheric, terrifying, thought-provoking.
* **Exclude:** Graphic gore, jump-scare-driven, purely action-based.

**Touchstone Shows (for calibration):**
* The Haunting of Hill House (2018)
* Severance (2022)
* Midnight Mass (2021)

**Your Task:**
List 5 {{movieType}} that perfectly match this recipe. Briefly state the source of the suspense in each series.`,
        tvprompt3: `Find {{movieType}} matching the following tag profile.

**//-- MUST INCLUDE --//**
+horror +suspense +psychological_horror +atmospheric +dread +slow_burn

**//-- SHOULD INCLUDE --//**
+thriller +mystery +supernatural

**//-- MUST NOT INCLUDE --//**
-gore -splatter -comedy -action

**//-- EXAMPLES --//**
+Black Mirror (2011)
+The Outsider (2020)
+Archive 81 (2022)

**//-- OUTPUT --//**
Return a list of 5 {{movieType}} with a 1-sentence justification for each.`,
        bg_image: 'spine_chillers.jpg',
        bg_gif: 'spine_chillers.gif',
        is_visible: '1'
    },
    {
        genre_id: '8',
        name: 'Stormy Night',
        name_slug: 'stormy-night',
        description: 'Settle in for a thrilling night as these captivating movies unfold amidst the fury of a storm, where suspense, mystery, and unexpected twists combine to create an atmospheric and gripping cinematic experience.',
        prompt1: `{{movieType}} Recommendations: Stormy Night

Core concept: A suspense, mystery, or horror film where a storm is a key plot device, trapping characters and amplifying tension.
Keywords: contained thriller, stormy night, mystery, suspense, atmospheric.
Examples: Identity, Clue, The Hateful Eight.
Avoid: Disaster movies focused on the storm's destruction.

List 5 {{movieType}} that fit.`,
        prompt2: `Generate {{movieType}} recommendations based on the following genre recipe.

**Genre Profile: The Storm-Bound Thriller**

**Setting Formula:**
* **Base:** 1 Isolated Location (hotel, mansion, lighthouse)
* **Weather:** 1 Raging Storm (rain, blizzard, hurricane)
* **Result:** Characters are trapped.

**Plot Formula:**
* **Core:** A mystery, suspense, or horror that unfolds within the confined group.

**Tonal Profile:**
* **Include:** Claustrophobic, atmospheric, tense, dramatic, mysterious.
* **Exclude:** Open-world, action-adventure, disaster spectacle.

**Touchstone Films (for calibration):**
* Key Largo (1948)
* Shutter Island (2010)
* Bad Times at the El Royale (2018)

**Your Task:**
List 5 {{movieType}} that perfectly match this recipe. Briefly describe how the storm traps the characters.`,
        prompt3: `Find {{movieType}} matching the following tag profile.

**//-- MUST INCLUDE --//**
+contained_thriller +storm +mystery +suspense +atmospheric +claustrophobic

**//-- SHOULD INCLUDE --//**
+ensemble_cast +isolated_location +whodunnit

**//-- MUST NOT INCLUDE --//**
-disaster_movie -action_adventure -open_world

**//-- EXAMPLES --//**
+Identity (2003)
+Clue (1985)
+The Thing (1982)

**//-- OUTPUT --//**
Return a list of 5 {{movieType}} with a 1-sentence justification for each.`,
        tvprompt1: `{{movieType}} Recommendations: Stormy Night

Core concept: A limited series or season where characters are trapped in an isolated location by a storm or similar event, forcing secrets and tensions to the surface.
Keywords: contained thriller, isolation, mystery, suspense, atmospheric.
Examples: The Head, From, The White Lotus (uses social/emotional entrapment).
Avoid: Shows where the storm is only a minor plot point.

List 5 {{movieType}} that fit.`,
        tvprompt2: `Generate {{movieType}} recommendations based on the following genre recipe.

**Genre Profile: The Isolated Thriller Series**

**Setting Formula:**
* **Base:** 1 Isolated Location (research station, mysterious town, remote resort).
* **Trap:** A storm, a supernatural barrier, or psychological confinement.
* **Result:** Characters cannot leave.

**Plot Formula:**
* **Core:** A season-long mystery, power struggle, or survival scenario unfolds within the trapped group.

**Tonal Profile:**
* **Include:** Claustrophobic, atmospheric, tense, dramatic, mysterious.
* **Exclude:** Open-world, light-hearted, case-of-the-week.

**Touchstone Shows (for calibration):**
* The Head (2020)
* From (2022)
* A Murder at the End of the World (2023)

**Your Task:**
List 5 {{movieType}} that perfectly match this recipe. Briefly describe the nature of the entrapment.`,
        tvprompt3: `Find {{movieType}} matching the following tag profile.

**//-- MUST INCLUDE --//**
+contained_thriller +isolation +mystery +suspense +atmospheric +claustrophobic

**//-- SHOULD INCLUDE --//**
+ensemble_cast +remote_location +whodunnit

**//-- MUST NOT INCLUDE --//**
-disaster_movie -action_adventure -open_world

**//-- EXAMPLES --//**
+The Terror (Season 1) (2018)
+Slasher (some seasons, e.g., 'Flesh & Blood') (2016)
+And Then There Were None (2015)

**//-- OUTPUT --//**
Return a list of 5 {{movieType}} with a 1-sentence justification for each.`,
        bg_image: 'stormy_night.jpg',
        bg_gif: 'stormy_night.gif',
        is_visible: '1'
    },
    {
        genre_id: '9',
        name: 'Stories on the Move',
        name_slug: 'train-ship-plane',
        description: 'Embark on a suspenseful journey on various modes of transportation, from the claustrophobic confines of a train to the vast expanse of the open sea or the high-altitude thrills of an aeroplane, as danger and intrigue unfold.',
        prompt1: `{{movieType}} Recommendations: Stories on the Move

Core concept: An action, suspense, or mystery film set almost entirely on a moving vehicle like a train, plane, or ship.
Keywords: train, plane, ship, contained thriller, action, suspense.
Examples: Speed, Murder on the Orient Express, Non-Stop.
Avoid: Simple road trip movies without high stakes.

List 5 {{movieType}} that fit.`,
        prompt2: `Generate {{movieType}} recommendations based on the following genre recipe.

**Genre Profile: The High-Velocity Thriller**

**Setting Formula:**
* **Base:** 1 Single Vehicle in Constant Motion (train, plane, bus, ship, submarine).
* **Environment:** Claustrophobic and inescapable.

**Conflict Formula:**
* **Core:** A high-stakes threat confined to the vehicle (hijacking, bomb, murder mystery, outbreak).

**Tonal Profile:**
* **Include:** Tense, claustrophobic, fast-paced, suspenseful, action-packed.
* **Exclude:** Slow-burn drama, road trip comedy.

**Touchstone Films (for calibration):**
* Snowpiercer (2013)
* Air Force One (1997)
* Unstoppable (2010)

**Your Task:**
List 5 {{movieType}} that perfectly match this recipe. Name the vehicle at the center of the plot.`,
        prompt3: `Find {{movieType}} matching the following tag profile.

**//-- MUST INCLUDE --//**
+contained_thriller +action +suspense +train +plane +ship +bus

**//-- SHOULD INCLUDE --//**
+fast_paced +high_stakes +claustrophobic

**//-- MUST NOT INCLUDE --//**
-road_trip_comedy -drama -slow_burn

**//-- EXAMPLES --//**
+Bullet Train (2022)
+Source Code (2011)
+Flightplan (2005)

**//-- OUTPUT --//**
Return a list of 5 {{movieType}} with a 1-sentence justification for each.`,
        tvprompt1: `{{movieType}} Recommendations: Stories on the Move

Core concept: A series where the primary setting is a moving vehicle, driving a season-long plot of action, mystery, or survival.
Keywords: train, plane, ship, submarine, contained thriller, sci-fi, suspense.
Examples: Snowpiercer, The Last Ship, Into the Night.
Avoid: Shows where travel is only a small part of the story.

List 5 {{movieType}} that fit.`,
        tvprompt2: `Generate {{movieType}} recommendations based on the following genre recipe.

**Genre Profile: The Contained Journey Series**

**Setting Formula:**
* **Base:** A single, large vehicle in constant motion (train, plane, ship).
* **Environment:** It is the characters' entire world for the season.

**Conflict Formula:**
* **Core:** A high-stakes, season-long crisis unfolds on board (post-apocalyptic survival, military mission, ongoing mystery).

**Tonal Profile:**
* **Include:** Tense, claustrophobic, serialized, action-packed, suspenseful.
* **Exclude:** Episodic, light-hearted, travel documentary.

**Touchstone Shows (for calibration):**
* Snowpiercer (the series) (2020)
* The Last Ship (2014)
* Y: The Last Man (2021)

**Your Task:**
List 5 {{movieType}} that perfectly match this recipe. Name the primary vehicle that serves as the setting.`,
        tvprompt3: `Find {{movieType}} matching the following tag profile.

**//-- MUST INCLUDE --//**
+contained_thriller +suspense +train +plane +ship +serialized

**//-- SHOULD INCLUDE --//**
+post_apocalyptic +sci_fi +action +mystery

**//-- MUST NOT INCLUDE --//**
-episodic -comedy -drama

**//-- EXAMPLES --//**
+Into the Night (2020)
+Hijack (2023)
+Vigil (2021)

**//-- OUTPUT --//**
Return a list of 5 {{movieType}} with a 1-sentence justification for each.`,
        bg_image: 'train_ship_plane.jpg',
        bg_gif: 'train_ship_plane.gif',
        is_visible: '1'
    },
    {
        genre_id: '10',
        name: 'Whodunit',
        name_slug: 'whodunit',
        description: 'Step into the opulent halls of a mysterious castle as these captivating whodunit movies unravel complex mysteries, intricate webs of deceit, and shocking revelations in a setting steeped in intrigue.',
        prompt1: `{{movieType}} Recommendations: Whodunit

Core concept: A classic murder mystery where a detective investigates an ensemble of suspects in a contained setting to find the killer.
Keywords: whodunnit, murder mystery, detective, ensemble cast, red herring, investigation.
Examples: Clue, Gosford Park, Murder on the Orient Express.
Avoid: Thrillers where the killer is known to the audience.

List 5 {{movieType}} that fit.`,
        prompt2: `Generate {{movieType}} recommendations based on the following genre recipe.

**Genre Profile: The Classic Whodunit**

**Plot Formula:**
* **Base:** 1 Baffling Murder in a Contained Setting.
* **Mix-ins:** An ensemble of secretive suspects, each with a motive.

**Detective Formula:**
* **Core:** A brilliant investigator (official or amateur) gathers clues.
* **Climax:** The 'parlor scene' reveal where the entire plot is deconstructed and the killer is exposed.

**Tonal Profile:**
* **Include:** Mysterious, intricate, clever, suspenseful, dialogue-driven.
* **Exclude:** Open-world crime stories, spy thrillers, action-heavy plots.

**Touchstone Films (for calibration):**
* Knives Out (2019)
* The Last of Sheila (1973)
* Death on the Nile (1978)

**Your Task:**
List 5 {{movieType}} that perfectly match this recipe. Briefly describe the central mystery.`,
        prompt3: `Find {{movieType}} matching the following tag profile.

**//-- MUST INCLUDE --//**
+whodunnit +murder_mystery +detective +ensemble_cast +investigation +reveal

**//-- SHOULD INCLUDE --//**
+red_herring +contained_setting +period_piece

**//-- MUST NOT INCLUDE --//**
-police_procedural -spy_movie -killer_is_known -action_thriller

**//-- EXAMPLES --//**
+A Haunting in Venice (2023)
+See How They Run (2022)
+Gosford Park (2001)

**//-- OUTPUT --//**
Return a list of 5 {{movieType}} with a 1-sentence justification for each.`,
        tvprompt1: `{{movieType}} Recommendations: Whodunit

Core concept: A murder mystery series where a detective uncovers the killer from an ensemble of suspects, often over a season-long arc.
Keywords: whodunnit, murder mystery, detective, investigation, red herring.
Examples: Only Murders in the Building, Broadchurch, Mare of Easttown.
Avoid: Standard police procedurals where the focus isn't the puzzle.

List 5 {{movieType}} that fit.`,
        tvprompt2: `Generate {{movieType}} recommendations based on the following genre recipe.

**Genre Profile: The Whodunit Series**

**Plot Formula:**
* **Base:** 1 Baffling Murder with a season-long investigation.
* **Mix-ins:** An ensemble of secretive suspects in a close-knit community.

**Detective Formula:**
* **Core:** A brilliant or deeply flawed investigator gathers clues.
* **Climax:** The final episodes reveal the killer and their motive in a dramatic conclusion.

**Tonal Profile:**
* **Include:** Mysterious, intricate, clever, suspenseful, character-driven.
* **Exclude:** Action-heavy, spy thrillers, simple open-and-shut cases.

**Touchstone Shows (for calibration):**
* Mare of Easttown (2021)
* Agatha Christie's Poirot (1989)
* The Afterparty (2022)

**Your Task:**
List 5 {{movieType}} that perfectly match this recipe. Briefly describe the central mystery of the first season.`,
        tvprompt3: `Find {{movieType}} matching the following tag profile.

**//-- MUST INCLUDE --//**
+whodunnit +murder_mystery +detective +ensemble_cast +investigation +reveal

**//-- SHOULD INCLUDE --//**
+red_herring +small_town_secrets +serialized_mystery

**//-- MUST NOT INCLUDE --//**
-police_procedural -spy_movie -killer_is_known

**//-- EXAMPLES --//**
+Broadchurch (2013)
+Only Murders in the Building (2021)
+A Murder at the End of the World (2023)

**//-- OUTPUT --//**
Return a list of 5 {{movieType}} with a 1-sentence justification for each.`,
        bg_image: 'whodunit.jpg',
        bg_gif: 'whodunit.gif',
        is_visible: '1'
    }
];
