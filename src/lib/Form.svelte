<script>
	import SpecialGenreCardGif from './SpecialGenreCardGif.svelte';
	import SpecialGenreCardImg from './SpecialGenreCardImg.svelte';
	import GenreCard from './GenreCard.svelte';

	import { categoryTypes } from '$lib/constants/CategoryTypes';
	import { specialGenres } from '$lib/constants/SpecialGenres';

	/**
	 * @type string
	 */
	export let cinemaType;
	/**
	 * @type Array<string>
	 */
	export let selectedCategories;
	/**
	 * @type string
	 * For open prompt textarea
	 */
	// export let userTypedPrompt;

	// Removed unused export 'loading' as it is not referenced in this component.
	// export let loading;

	/**
	 * @type {string}
	 */
	export let specialGenreSelected = '';

	let cinemaTypes = [
		{ value: 'movie', title: 'Movie' },
		{ value: 'tv show', title: 'TV Show' }
	];
</script>

<div class="pt-6 md:pt-10 text-slate-200">
	<div>
		<div class="mb-8">
			<div class="mb-4 font-semibold text-2xl">What kind of cinema are you searching for?</div>
			<div class="flex items-center">
				{#each cinemaTypes as type (type.value)}
					<button
						type="button"
						on:click={() => {
							cinemaType = type.value;
						}}
						class={`btn-cinema-type ${
							cinemaType === type.value ? 'bg-turquoise-600/40' : ''
						} font-bold mr-2 text-sm mt-2 py-2 px-4 rounded-full border border-turquoise-600 ${
							cinemaType === type.value ? 'text-primary' : 'text-muted'
						}`}
					>
						{type.title}
					</button>
				{/each}
			</div>
		</div>
		
		{#if Math.random() > 0.9}
			<SpecialGenreCardGif bind:specialGenreSelected {specialGenres} />
		{:else}
			<SpecialGenreCardImg bind:specialGenreSelected {specialGenres} />
		{/if}

		<div class="my-6"></div>

		<GenreCard bind:selectedCategories {categoryTypes} />

		<!-- Use <UserOpenPrompt /> here if you wish -->
	</div>
</div>
