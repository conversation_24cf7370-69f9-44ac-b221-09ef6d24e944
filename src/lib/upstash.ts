import { KV_REST_API_URL, KV_REST_API_TOKEN } from '$env/static/private';

interface RateLimitData {
	count: number;
	lastResetTime: number;
}

export class UpstashRateLimit {
	private baseUrl: string;
	private token: string;

	constructor() {
		this.baseUrl = KV_REST_API_URL;
		this.token = KV_REST_API_TOKEN;
	}

	private async makeRequest(method: string, key: string, value?: any) {
		let url: string;
		let options: RequestInit;

		if (method === 'get') {
			url = `${this.baseUrl}/get/${encodeURIComponent(key)}`;
			options = {
				method: 'GET',
				headers: {
					'Authorization': `Bearer ${this.token}`
				}
			};
		} else if (method === 'set') {
			url = `${this.baseUrl}/set/${encodeURIComponent(key)}`;
			options = {
				method: 'POST',
				headers: {
					'Authorization': `Bearer ${this.token}`,
					'Content-Type': 'application/json'
				},
				body: JSON.stringify(value)
			};
		} else {
			throw new Error(`Unsupported method: ${method}`);
		}

		const response = await fetch(url, options);

		if (!response.ok) {
			const errorText = await response.text();
			throw new Error(`Upstash request failed: ${response.statusText} - ${errorText}`);
		}

		return response.json();
	}

	async getRateLimitData(ip: string): Promise<RateLimitData | null> {
		try {
			const result = await this.makeRequest('get', ip);
			if (!result.result) return null;

			// Handle both string and object responses
			if (typeof result.result === 'string') {
				return JSON.parse(result.result);
			} else {
				return result.result;
			}
		} catch (error) {
			console.error('Error getting rate limit data:', error);
			return null;
		}
	}

	async setRateLimitData(ip: string, data: RateLimitData): Promise<void> {
		try {
			await this.makeRequest('set', ip, data);
		} catch (error) {
			console.error('Error setting rate limit data:', error);
			throw error;
		}
	}

	async checkAndUpdateRateLimit(ip: string): Promise<{ allowed: boolean; count: number; resetTime: number }> {
		const now = Date.now();
		const oneHour = 60 * 60 * 1000; // 1 hour in milliseconds

		// Get current rate limit data
		let rateLimitData = await this.getRateLimitData(ip);

		// If no data exists or it's been more than an hour since last reset, create new data
		if (!rateLimitData || !rateLimitData.lastResetTime || (now - rateLimitData.lastResetTime) >= oneHour) {
			const newData: RateLimitData = {
				count: 1,
				lastResetTime: now
			};
			await this.setRateLimitData(ip, newData);
			return { allowed: true, count: 1, resetTime: now };
		}

		// Check if limit exceeded
		if (rateLimitData.count >= 50) {
			return { allowed: false, count: rateLimitData.count, resetTime: rateLimitData.lastResetTime };
		}

		// Create new data object with incremented count, keeping the same reset time
		const updatedData: RateLimitData = {
			count: rateLimitData.count + 1,
			lastResetTime: rateLimitData.lastResetTime
		};
		await this.setRateLimitData(ip, updatedData);

		return { allowed: true, count: updatedData.count, resetTime: rateLimitData.lastResetTime };
	}

	async getCurrentCount(ip: string): Promise<{ count: number; resetTime: number }> {
		const now = Date.now();
		const oneHour = 60 * 60 * 1000;

		const rateLimitData = await this.getRateLimitData(ip);

		// If no data exists or it's been more than an hour since last reset, return 0 with current time
		if (!rateLimitData || !rateLimitData.lastResetTime || (now - rateLimitData.lastResetTime) >= oneHour) {
			return { count: 0, resetTime: now };
		}

		// Return current count and the actual reset time from when the counter was last reset
		return { count: rateLimitData.count || 0, resetTime: rateLimitData.lastResetTime };
	}
}

export const upstashRateLimit = new UpstashRateLimit();
