<script>
	import { Countries } from '$lib/constants/Countries';
	import { createEventDispatcher } from 'svelte';

	const dispatch = createEventDispatcher();

	let email = '';
	let selectedCountry = '';
	let loading = false;
	let error = '';
	let success = false;
	let successMessage = '';

	async function handleRegistrationRequest() {
		if (!email || !selectedCountry) {
			error = 'Please fill in all fields';
			return;
		}

		loading = true;
		error = '';

		try {
			const response = await fetch('/api/register', {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json'
				},
				body: JSON.stringify({
					email: email,
					country_code: selectedCountry
				})
			});

			if (response.ok) {
				const data = await response.json();
				success = true;
				successMessage = data.message;
				// Reset form
				email = '';
				selectedCountry = '';
			} else {
				const errorText = await response.text();
				error = errorText || 'Registration request failed';
			}
		} catch (err) {
			error = 'Network error. Please try again.';
		}

		loading = false;
	}

	function goBackToLogin() {
		dispatch('back');
	}
</script>

<div class="w-full">
	<div
		class="text-center md:text-left font-bold text-slate-200 text-4xl md:text-5xl mb-8 md:mb-16 w-full"
	>
		Request Access
		<br class="hidden md:block" /> to Movie Recommendations
	</div>
	
	<div class="max-w-md mx-auto md:mx-0 bg-white/10 backdrop-blur-sm rounded-lg p-6 mb-8">
		{#if success}
			<div class="text-center">
				<div class="bg-green-500/20 border border-green-500 text-green-200 px-4 py-3 rounded mb-4">
					<h3 class="font-semibold mb-2">Request Submitted!</h3>
					<p class="text-sm">{successMessage}</p>
				</div>
				<button
					on:click={goBackToLogin}
					class="bg-turquoise-600 hover:bg-turquoise-700 text-white font-medium py-2 px-4 rounded-md"
				>
					Back to Login
				</button>
			</div>
		{:else}
			<div class="bg-blue-500/20 border border-blue-500 text-blue-200 px-4 py-3 rounded mb-6">
				<p class="text-sm">
					To access the movie recommendation, please send a request with your email address and your country. We will reply you a secret key. Then you can use the secret key to login.
				</p>
			</div>

			{#if error}
				<div class="bg-red-500/20 border border-red-500 text-red-200 px-4 py-2 rounded mb-4">
					{error}
				</div>
			{/if}

			<form on:submit|preventDefault={handleRegistrationRequest} class="space-y-4">
				<div>
					<label for="email" class="block text-white text-sm font-medium mb-2">
						Email Address
					</label>
					<input
						id="email"
						type="email"
						bind:value={email}
						required
						class="w-full px-3 py-2 bg-white/20 border border-white/30 rounded-md text-white placeholder-white/60 focus:outline-none focus:ring-2 focus:ring-turquoise-500 focus:border-transparent"
						placeholder="Enter your email"
					/>
				</div>

				<div>
					<label for="country" class="block text-white text-sm font-medium mb-2">
						Country
					</label>
					<select
						id="country"
						bind:value={selectedCountry}
						required
						class="w-full px-3 py-2 bg-white/20 border border-white/30 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-turquoise-500 focus:border-transparent"
					>
						<option value="" disabled>Select your country</option>
						{#each Countries as country}
							<option value={country.country_code} class="bg-slate-800 text-white">
								{country.country_name}
							</option>
						{/each}
					</select>
				</div>

				<!-- Legal Compliance Notice -->
				<div class="bg-slate-700/30 border border-slate-600 rounded-md p-4 text-xs text-white/70">
					<p class="mb-2">
						By submitting this registration request, you agree to our
						<a href="/terms" target="_blank" class="text-turquoise-400 hover:text-turquoise-300 underline">Terms of Service</a>
						and acknowledge that you have read our
						<a href="/privacy" target="_blank" class="text-turquoise-400 hover:text-turquoise-300 underline">Privacy Policy</a>.
					</p>
					<p>
						We will collect and process your email address and country information as described in our Privacy Policy.
						Your data will be used solely for account management and providing personalized recommendations.
					</p>
				</div>

				<div class="flex flex-col space-y-3">
					<button
						type="submit"
						disabled={loading}
						class="btn-submit-registration w-full bg-turquoise-600 hover:bg-turquoise-700 disabled:bg-turquoise-600/50 disabled:cursor-not-allowed text-white font-medium py-3 px-6 rounded-md transition-colors"
					>
						{loading ? 'Submitting Request...' : 'Submit Registration Request'}
					</button>

					<button
						type="button"
						on:click={goBackToLogin}
						class="btn-back-to-login w-full bg-gray-600 hover:bg-gray-700 text-white font-medium py-2 px-6 rounded-md transition-colors"
					>
						Back to Login
					</button>
				</div>
			</form>
		{/if}
	</div>
</div>
