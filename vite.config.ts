import { sveltekit } from '@sveltejs/kit/vite';
import type { UserConfig } from 'vite';

const config: UserConfig = {
	plugins: [sveltekit()],
	define: {
		global: 'globalThis'
	},
	ssr: {
		noExternal: [],
		external: ['mongodb', 'bson', '@mongodb-js/saslprep', 'mongodb-connection-string-url']
	},
	optimizeDeps: {
		exclude: ['mongodb', 'bson', '@mongodb-js/saslprep', 'mongodb-connection-string-url']
	}
};

export default config;
