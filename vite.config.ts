import { sveltekit } from '@sveltejs/kit/vite';
import { SvelteKitPWA } from '@vite-pwa/sveltekit';
import type { UserConfig } from 'vite';

const config: UserConfig = {
	plugins: [
		sveltekit(),
		SvelteKitPWA({
			srcDir: './src',
			mode: 'development',
			strategies: 'generateSW',
			scope: '/',
			base: '/',
			selfDestroying: process.env.NODE_ENV === 'development',
			manifest: {
				short_name: 'Cinemated',
				name: 'Cinemated - AI Movie & TV Recommendations',
				start_url: '/',
				scope: '/',
				display: 'standalone',
				theme_color: '#1de9b6',
				background_color: '#0f172a',
				icons: [
					{
						src: '/icon-192x192.png',
						sizes: '192x192',
						type: 'image/png',
						purpose: 'maskable any'
					},
					{
						src: '/icon-512x512.png',
						sizes: '512x512',
						type: 'image/png',
						purpose: 'maskable any'
					}
				]
			},
			workbox: {
				globPatterns: ['**/*.{js,css,html,svg,png,ico,webp}'],
				maximumFileSizeToCacheInBytes: 5 * 1024 * 1024, // 5MB
				runtimeCaching: [
					{
						urlPattern: /^https:\/\/api\./,
						handler: 'NetworkFirst',
						options: {
							cacheName: 'api-cache',
							networkTimeoutSeconds: 10,
							cacheableResponse: {
								statuses: [0, 200]
							}
						}
					},
					{
						urlPattern: /\.(?:png|jpg|jpeg|svg|gif|webp|ico)$/,
						handler: 'CacheFirst',
						options: {
							cacheName: 'images-cache',
							expiration: {
								maxEntries: 100,
								maxAgeSeconds: 60 * 60 * 24 * 30 // 30 days
							}
						}
					}
				]
			},
			devOptions: {
				enabled: true,
				suppressWarnings: process.env.NODE_ENV === 'development',
				type: 'module',
				navigateFallback: '/'
			},
			kit: {
				trailingSlash: 'never'
			}
		})
	],
	define: {
		global: 'globalThis'
	},
	ssr: {
		noExternal: [],
		external: ['mongodb', 'bson', '@mongodb-js/saslprep', 'mongodb-connection-string-url']
	},
	optimizeDeps: {
		exclude: ['mongodb', 'bson', '@mongodb-js/saslprep', 'mongodb-connection-string-url']
	}
};

export default config;
