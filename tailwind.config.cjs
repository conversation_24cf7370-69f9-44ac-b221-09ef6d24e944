/** @type {import('tailwindcss').Config} */
module.exports = {
	content: ['./src/**/*.{html,js,svelte,ts}'],
	darkMode: 'class', // Enable class-based dark mode
	theme: {
		extend: {
			colors: {
				turquoise: {
					DEFAULT: '#1DE9B6',
					dark: '#13bfa6', // optional, for hover/dark variants
					50:  '#e0fcf7',
					100: '#b3f7e9',
					200: '#80f2db',
					300: '#4dedcd',
					400: '#26e9c2',
					500: '#1de9b6', // your main color
					600: '#18c9a0',
					700: '#13b98e',
					800: '#0e8c6b',
					900: '#095d48',
				},
			},
		}
	},
	plugins: []
};
