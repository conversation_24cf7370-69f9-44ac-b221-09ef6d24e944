{"name": "Cinemated - AI Movie & TV Recommendations", "short_name": "Cinemated", "description": "Get personalized movie and TV show recommendations powered by AI technology", "start_url": "/", "display": "standalone", "background_color": "#0f172a", "theme_color": "#1de9b6", "orientation": "portrait-primary", "scope": "/", "lang": "en", "categories": ["entertainment", "lifestyle"], "icons": [{"src": "/icon-72x72.png", "sizes": "72x72", "type": "image/png", "purpose": "maskable any"}, {"src": "/icon-96x96.png", "sizes": "96x96", "type": "image/png", "purpose": "maskable any"}, {"src": "/icon-128x128.png", "sizes": "128x128", "type": "image/png", "purpose": "maskable any"}, {"src": "/icon-144x144.png", "sizes": "144x144", "type": "image/png", "purpose": "maskable any"}, {"src": "/icon-152x152.png", "sizes": "152x152", "type": "image/png", "purpose": "maskable any"}, {"src": "/icon-192x192.png", "sizes": "192x192", "type": "image/png", "purpose": "maskable any"}, {"src": "/icon-384x384.png", "sizes": "384x384", "type": "image/png", "purpose": "maskable any"}, {"src": "/icon-512x512.png", "sizes": "512x512", "type": "image/png", "purpose": "maskable any"}], "shortcuts": [{"name": "Get Recommendations", "short_name": "Recommendations", "description": "Get AI-powered movie and TV recommendations", "url": "/recommendations", "icons": [{"src": "/icon-192x192.png", "sizes": "192x192"}]}], "screenshots": [{"src": "/screenshot-wide.png", "sizes": "1280x720", "type": "image/png", "form_factor": "wide"}, {"src": "/screenshot-narrow.png", "sizes": "390x844", "type": "image/png", "form_factor": "narrow"}]}