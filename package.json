{"name": "tv-recs-openai", "version": "0.0.1", "private": true, "scripts": {"dev": "vite dev", "build": "svelte-kit sync && vite build", "preview": "vite preview", "check": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json", "check:watch": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json --watch", "lint": "prettier --plugin-search-dir . --check . && eslint .", "format": "prettier --plugin-search-dir . --write ."}, "devDependencies": {"@sveltejs/adapter-auto": "^3.2.5", "@sveltejs/kit": "^2.8.5", "@sveltejs/vite-plugin-svelte": "^3.1.2", "@typescript-eslint/eslint-plugin": "^6.21.0", "@typescript-eslint/parser": "^6.21.0", "autoprefixer": "^10.4.20", "eslint": "^8.57.1", "eslint-config-prettier": "^9.1.0", "postcss": "^8.4.47", "prettier": "^3.3.3", "prettier-plugin-svelte": "^3.2.8", "svelte": "^4.2.19", "svelte-check": "^4.0.8", "tailwindcss": "^3.4.15", "tslib": "^2.8.1", "typescript": "^5.7.2", "vite": "^5.4.10"}, "type": "module", "dependencies": {"@sveltejs/adapter-vercel": "^5.7.2", "@types/geoip-lite": "^1.4.4", "@vercel/analytics": "^1.3.2", "@vercel/kv": "^3.0.0", "@xenova/transformers": "^2.17.2", "esbuild": "^0.24.0", "eventsource-parser": "^3.0.0", "geoip-lite": "^1.4.10", "mongodb": "^6.10.0"}}