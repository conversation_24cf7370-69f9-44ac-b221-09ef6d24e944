# 🎬 Cinemated - AI-Powered Movie & TV Recommendation Platform

**Version 1.0.0** - First Release

Cinemated is a sophisticated web application that provides personalized movie and TV show recommendations using AI technology. Built with SvelteKit, TypeScript, and MongoDB, it offers a complete user management system with email verification, rate limiting, and comprehensive admin controls.

## ✨ Key Features

### 🤖 AI-Powered Recommendations
- **Intelligent Suggestions**: Get 5 personalized movie/TV recommendations based on your preferences
- **Genre Selection**: Choose from comprehensive genre categories including special genres
- **Custom Prompts**: Describe what you're looking for in natural language
- **Pagination System**: Get more recommendations with a simple click (up to 50 per hour)
- **Localized Results**: Recommendations tailored to your country and region

### 🔐 Secure User Management
- **Email-Based Registration**: Request access with email and country selection
- **Admin Approval System**: Manual approval process for quality control
- **Email Verification**: Secure email verification with unique codes
- **Secret Key Authentication**: Secure login system with generated secret keys
- **Rate Limiting**: 50 recommendation requests per hour per user
- **IP Tracking**: Security monitoring with registration and login IP tracking

### 👨‍💼 Comprehensive Admin Panel
- **Four-Tab Organization**:
  - **Verified Users**: Manage users who completed email verification
  - **Pending Email Verification**: Handle users awaiting verification
  - **Rejected Users**: Review and manage rejected registration requests
  - **Registration Requests**: Process new user applications
- **User Actions**: Enable/disable accounts, resend verification emails
- **Professional Email Templates**: Ready-to-send welcome emails with verification links
- **Copy Functionality**: One-click copying of emails, secret keys, and verification links
- **Visual Feedback**: Toast notifications for all copy operations

### 🎭 Rich Content Details
- **TMDB Integration**: Comprehensive movie and TV show metadata
- **Cast & Crew Information**: Detailed cast lists with character information
- **Streaming Providers**: Where to watch content (powered by JustWatch)
- **Production Details**: Studios, companies, and production information
- **Ratings & Reviews**: TMDB ratings and user scores
- **Trailers & Videos**: Embedded video content
- **Season Information**: Detailed TV show season and episode data

### 🌍 Localization & Attribution
- **Country-Based Results**: Streaming providers and content based on user location
- **Proper Attribution**: TMDB and JustWatch logos with homepage links
- **Legal Compliance**: Full attribution footer and contextual credits
- **Responsive Design**: Works seamlessly on desktop and mobile devices

## 🛠 Technology Stack

### Frontend
- **SvelteKit**: Modern web framework with TypeScript support
- **Tailwind CSS**: Utility-first CSS framework for responsive design
- **Vite**: Fast build tool and development server

### Backend
- **Node.js**: Server-side JavaScript runtime
- **MongoDB**: NoSQL database for user and application data
- **API Integration**: External AI and movie database services

### Security & Performance
- **Rate Limiting**: In-memory request tracking with sliding window
- **Authentication**: Header-based user authentication
- **Input Validation**: Comprehensive request validation
- **Error Handling**: Graceful error handling with user-friendly messages

## 🚀 Getting Started

### Prerequisites
- Node.js 18+
- MongoDB database
- Environment variables configured

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd cinemated-web-typescript-svelte
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Configure environment variables**
   Create `.env` file with:
   ```env
   MONGODB_URI_KAMATERA=your_mongodb_connection_string
   ADMIN_EMAIL=<EMAIL>
   API_URL_DEV=your_development_api_url
   API_URL_PROD=your_production_api_url
   API_KEY=your_api_key
   ```

4. **Start development server**
   ```bash
   npm run dev
   ```

5. **Access the application**
   - Open `http://localhost:5173`
   - Register for an account
   - Wait for admin approval
   - Verify your email
   - Start getting recommendations!

## 📱 User Journey

### For Regular Users
1. **Registration**: Submit email and country for access request
2. **Approval**: Wait for admin to approve your registration
3. **Verification**: Click email verification link and receive secret key
4. **Login**: Use email and secret key to access the platform
5. **Recommendations**: Get personalized movie/TV suggestions
6. **Explore**: View detailed information and streaming options

### For Administrators
1. **Access Admin Panel**: Login with admin email to access controls
2. **Process Requests**: Approve or reject registration requests
3. **Send Welcome Emails**: Copy and send professional welcome emails
4. **Manage Users**: Enable/disable accounts, resend verifications
5. **Monitor Activity**: Track user activity and IP addresses

## 🎨 Design Features

### User Interface
- **Dark Theme**: Professional dark color scheme
- **Responsive Layout**: Optimized for all screen sizes
- **Smooth Animations**: Slide transitions and hover effects
- **Visual Feedback**: Loading states, success messages, and error handling
- **Accessibility**: Proper ARIA labels and keyboard navigation

### Admin Experience
- **Professional Modals**: Beautiful modal windows for user management
- **Copy Functionality**: Easy copying of credentials and email content
- **Visual Indicators**: Color-coded status badges and progress indicators
- **Organized Tabs**: Clear separation of different user states

## 🔒 Security Features

### Authentication & Authorization
- **Email-based authentication** with secret key system
- **Admin role management** with environment-based admin identification
- **Rate limiting** to prevent API abuse
- **IP tracking** for security monitoring

### Data Protection
- **Input validation** on all API endpoints
- **Secure secret key generation** without predictable patterns
- **Email verification** to confirm user identity
- **Database security** with proper connection handling

## 📊 API Endpoints

### Public Endpoints
- `POST /api/register` - Submit registration request
- `POST /api/login` - User authentication
- `POST /api/email-verify` - Email verification

### Authenticated Endpoints
- `POST /api/getRecommendation` - Get AI recommendations
- `POST /api/searchMovies` - Search movie details
- `POST /api/searchTvShows` - Search TV show details

### Admin Endpoints
- `GET /api/admin/users` - Manage users
- `GET /api/admin/registration-requests` - Handle requests
- `POST /api/admin/regenerate-verification` - Resend verification

## 🎯 Future Enhancements

- **Automated Email Service**: Integration with SendGrid or similar
- **Advanced Filtering**: More sophisticated recommendation filters
- **User Preferences**: Saved preferences and recommendation history
- **Social Features**: User reviews and recommendation sharing
- **Mobile App**: Native mobile application
- **Analytics Dashboard**: Usage statistics and insights

## 📄 License & Attribution

This project uses data from:
- **The Movie Database (TMDB)**: Movie and TV show metadata
- **JustWatch**: Streaming provider information

This product uses the TMDB API but is not endorsed or certified by TMDB.

## 🤝 Contributing

We welcome contributions! Please read our contributing guidelines and submit pull requests for any improvements.

## 📞 Support

For support or questions, please contact the development team or create an issue in the repository.

---

**Cinemated v1.0.0** - Built with ❤️ for movie and TV enthusiasts
