<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
 "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<!-- Generated by graphviz version 12.2.1 (20241206.2353)
 -->
<!-- Title: G Pages: 1 -->
<svg width="323pt" height="823pt"
 viewBox="0.00 0.00 323.20 822.95" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
<g id="graph0" class="graph" transform="scale(1 1) rotate(0) translate(21.6 801.35)">
<title>G</title>
<polygon fill="#111111" stroke="none" points="-21.6,21.6 -21.6,-801.35 301.6,-801.35 301.6,21.6 -21.6,21.6"/>
<!-- app.d.ts -->
<g id="node1" class="node">
<title>app.d.ts</title>
<path fill="none" stroke="#cfffac" d="M164.08,-23.75C164.08,-23.75 115.92,-23.75 115.92,-23.75 111.96,-23.75 108,-19.79 108,-15.83 108,-15.83 108,-7.92 108,-7.92 108,-3.96 111.96,0 115.92,0 115.92,0 164.08,0 164.08,0 168.04,0 172,-3.96 172,-7.92 172,-7.92 172,-15.83 172,-15.83 172,-19.79 168.04,-23.75 164.08,-23.75"/>
<text text-anchor="middle" x="140" y="-6.45" font-family="Arial" font-size="14.00" fill="#cfffac">app.d.ts</text>
</g>
<!-- lib/Footer.svelte -->
<g id="node2" class="node">
<title>lib/Footer.svelte</title>
<path fill="none" stroke="#cfffac" d="M187.33,-65.75C187.33,-65.75 92.67,-65.75 92.67,-65.75 88.71,-65.75 84.75,-61.79 84.75,-57.83 84.75,-57.83 84.75,-49.92 84.75,-49.92 84.75,-45.96 88.71,-42 92.67,-42 92.67,-42 187.33,-42 187.33,-42 191.29,-42 195.25,-45.96 195.25,-49.92 195.25,-49.92 195.25,-57.83 195.25,-57.83 195.25,-61.79 191.29,-65.75 187.33,-65.75"/>
<text text-anchor="middle" x="140" y="-48.45" font-family="Arial" font-size="14.00" fill="#cfffac">lib/Footer.svelte</text>
</g>
<!-- lib/Form.svelte -->
<g id="node3" class="node">
<title>lib/Form.svelte</title>
<path fill="none" stroke="#cfffac" d="M184.33,-107.75C184.33,-107.75 95.67,-107.75 95.67,-107.75 91.71,-107.75 87.75,-103.79 87.75,-99.83 87.75,-99.83 87.75,-91.92 87.75,-91.92 87.75,-87.96 91.71,-84 95.67,-84 95.67,-84 184.33,-84 184.33,-84 188.29,-84 192.25,-87.96 192.25,-91.92 192.25,-91.92 192.25,-99.83 192.25,-99.83 192.25,-103.79 188.29,-107.75 184.33,-107.75"/>
<text text-anchor="middle" x="140" y="-90.45" font-family="Arial" font-size="14.00" fill="#cfffac">lib/Form.svelte</text>
</g>
<!-- lib/GitHubButton.svelte -->
<g id="node4" class="node">
<title>lib/GitHubButton.svelte</title>
<path fill="none" stroke="#cfffac" d="M209.08,-149.75C209.08,-149.75 70.92,-149.75 70.92,-149.75 66.96,-149.75 63,-145.79 63,-141.83 63,-141.83 63,-133.92 63,-133.92 63,-129.96 66.96,-126 70.92,-126 70.92,-126 209.08,-126 209.08,-126 213.04,-126 217,-129.96 217,-133.92 217,-133.92 217,-141.83 217,-141.83 217,-145.79 213.04,-149.75 209.08,-149.75"/>
<text text-anchor="middle" x="140" y="-132.45" font-family="Arial" font-size="14.00" fill="#cfffac">lib/GitHubButton.svelte</text>
</g>
<!-- lib/Header.svelte -->
<g id="node5" class="node">
<title>lib/Header.svelte</title>
<path fill="none" stroke="#cfffac" d="M189.96,-191.75C189.96,-191.75 90.04,-191.75 90.04,-191.75 86.08,-191.75 82.12,-187.79 82.12,-183.83 82.12,-183.83 82.12,-175.92 82.12,-175.92 82.12,-171.96 86.08,-168 90.04,-168 90.04,-168 189.96,-168 189.96,-168 193.92,-168 197.88,-171.96 197.88,-175.92 197.88,-175.92 197.88,-183.83 197.88,-183.83 197.88,-187.79 193.92,-191.75 189.96,-191.75"/>
<text text-anchor="middle" x="140" y="-174.45" font-family="Arial" font-size="14.00" fill="#cfffac">lib/Header.svelte</text>
</g>
<!-- lib/Home.svelte -->
<g id="node6" class="node">
<title>lib/Home.svelte</title>
<path fill="none" stroke="#cfffac" d="M186.58,-233.75C186.58,-233.75 93.42,-233.75 93.42,-233.75 89.46,-233.75 85.5,-229.79 85.5,-225.83 85.5,-225.83 85.5,-217.92 85.5,-217.92 85.5,-213.96 89.46,-210 93.42,-210 93.42,-210 186.58,-210 186.58,-210 190.54,-210 194.5,-213.96 194.5,-217.92 194.5,-217.92 194.5,-225.83 194.5,-225.83 194.5,-229.79 190.54,-233.75 186.58,-233.75"/>
<text text-anchor="middle" x="140" y="-216.45" font-family="Arial" font-size="14.00" fill="#cfffac">lib/Home.svelte</text>
</g>
<!-- lib/Loading.svelte -->
<g id="node7" class="node">
<title>lib/Loading.svelte</title>
<path fill="none" stroke="#cfffac" d="M192.21,-275.75C192.21,-275.75 87.79,-275.75 87.79,-275.75 83.83,-275.75 79.88,-271.79 79.88,-267.83 79.88,-267.83 79.88,-259.92 79.88,-259.92 79.88,-255.96 83.83,-252 87.79,-252 87.79,-252 192.21,-252 192.21,-252 196.17,-252 200.12,-255.96 200.12,-259.92 200.12,-259.92 200.12,-267.83 200.12,-267.83 200.12,-271.79 196.17,-275.75 192.21,-275.75"/>
<text text-anchor="middle" x="140" y="-258.45" font-family="Arial" font-size="14.00" fill="#cfffac">lib/Loading.svelte</text>
</g>
<!-- lib/LoadingCard.svelte -->
<g id="node8" class="node">
<title>lib/LoadingCard.svelte</title>
<path fill="none" stroke="#cfffac" d="M206.83,-317.75C206.83,-317.75 73.17,-317.75 73.17,-317.75 69.21,-317.75 65.25,-313.79 65.25,-309.83 65.25,-309.83 65.25,-301.92 65.25,-301.92 65.25,-297.96 69.21,-294 73.17,-294 73.17,-294 206.83,-294 206.83,-294 210.79,-294 214.75,-297.96 214.75,-301.92 214.75,-301.92 214.75,-309.83 214.75,-309.83 214.75,-313.79 210.79,-317.75 206.83,-317.75"/>
<text text-anchor="middle" x="140" y="-300.45" font-family="Arial" font-size="14.00" fill="#cfffac">lib/LoadingCard.svelte</text>
</g>
<!-- lib/PosterPlaceholder.svelte -->
<g id="node9" class="node">
<title>lib/PosterPlaceholder.svelte</title>
<path fill="none" stroke="#cfffac" d="M222.96,-359.75C222.96,-359.75 57.04,-359.75 57.04,-359.75 53.08,-359.75 49.12,-355.79 49.12,-351.83 49.12,-351.83 49.12,-343.92 49.12,-343.92 49.12,-339.96 53.08,-336 57.04,-336 57.04,-336 222.96,-336 222.96,-336 226.92,-336 230.88,-339.96 230.88,-343.92 230.88,-343.92 230.88,-351.83 230.88,-351.83 230.88,-355.79 226.92,-359.75 222.96,-359.75"/>
<text text-anchor="middle" x="140" y="-342.45" font-family="Arial" font-size="14.00" fill="#cfffac">lib/PosterPlaceholder.svelte</text>
</g>
<!-- lib/RecommendationCard.svelte -->
<g id="node10" class="node">
<title>lib/RecommendationCard.svelte</title>
<path fill="none" stroke="#cfffac" d="M236.46,-401.75C236.46,-401.75 43.54,-401.75 43.54,-401.75 39.58,-401.75 35.62,-397.79 35.62,-393.83 35.62,-393.83 35.62,-385.92 35.62,-385.92 35.62,-381.96 39.58,-378 43.54,-378 43.54,-378 236.46,-378 236.46,-378 240.42,-378 244.38,-381.96 244.38,-385.92 244.38,-385.92 244.38,-393.83 244.38,-393.83 244.38,-397.79 240.42,-401.75 236.46,-401.75"/>
<text text-anchor="middle" x="140" y="-384.45" font-family="Arial" font-size="14.00" fill="#cfffac">lib/RecommendationCard.svelte</text>
</g>
<!-- lib/RecommendationCardBuiltFromAI.svelte -->
<g id="node11" class="node">
<title>lib/RecommendationCardBuiltFromAI.svelte</title>
<path fill="none" stroke="#cfffac" d="M272.08,-443.75C272.08,-443.75 7.92,-443.75 7.92,-443.75 3.96,-443.75 0,-439.79 0,-435.83 0,-435.83 0,-427.92 0,-427.92 0,-423.96 3.96,-420 7.92,-420 7.92,-420 272.08,-420 272.08,-420 276.04,-420 280,-423.96 280,-427.92 280,-427.92 280,-435.83 280,-435.83 280,-439.79 276.04,-443.75 272.08,-443.75"/>
<text text-anchor="middle" x="140" y="-426.45" font-family="Arial" font-size="14.00" fill="#cfffac">lib/RecommendationCardBuiltFromAI.svelte</text>
</g>
<!-- lib/SpecialGenreCardGif.svelte -->
<g id="node12" class="node">
<title>lib/SpecialGenreCardGif.svelte</title>
<path fill="none" stroke="#cfffac" d="M233.08,-485.75C233.08,-485.75 46.92,-485.75 46.92,-485.75 42.96,-485.75 39,-481.79 39,-477.83 39,-477.83 39,-469.92 39,-469.92 39,-465.96 42.96,-462 46.92,-462 46.92,-462 233.08,-462 233.08,-462 237.04,-462 241,-465.96 241,-469.92 241,-469.92 241,-477.83 241,-477.83 241,-481.79 237.04,-485.75 233.08,-485.75"/>
<text text-anchor="middle" x="140" y="-468.45" font-family="Arial" font-size="14.00" fill="#cfffac">lib/SpecialGenreCardGif.svelte</text>
</g>
<!-- lib/SpecialGenreCardImg.svelte -->
<g id="node13" class="node">
<title>lib/SpecialGenreCardImg.svelte</title>
<path fill="none" stroke="#cfffac" d="M235.71,-527.75C235.71,-527.75 44.29,-527.75 44.29,-527.75 40.33,-527.75 36.37,-523.79 36.37,-519.83 36.37,-519.83 36.37,-511.92 36.37,-511.92 36.37,-507.96 40.33,-504 44.29,-504 44.29,-504 235.71,-504 235.71,-504 239.67,-504 243.62,-507.96 243.62,-511.92 243.62,-511.92 243.62,-519.83 243.62,-519.83 243.62,-523.79 239.67,-527.75 235.71,-527.75"/>
<text text-anchor="middle" x="140" y="-510.45" font-family="Arial" font-size="14.00" fill="#cfffac">lib/SpecialGenreCardImg.svelte</text>
</g>
<!-- lib/TvIcon.svelte -->
<g id="node14" class="node">
<title>lib/TvIcon.svelte</title>
<path fill="none" stroke="#cfffac" d="M188.46,-569.75C188.46,-569.75 91.54,-569.75 91.54,-569.75 87.58,-569.75 83.62,-565.79 83.62,-561.83 83.62,-561.83 83.62,-553.92 83.62,-553.92 83.62,-549.96 87.58,-546 91.54,-546 91.54,-546 188.46,-546 188.46,-546 192.42,-546 196.38,-549.96 196.38,-553.92 196.38,-553.92 196.38,-561.83 196.38,-561.83 196.38,-565.79 192.42,-569.75 188.46,-569.75"/>
<text text-anchor="middle" x="140" y="-552.45" font-family="Arial" font-size="14.00" fill="#cfffac">lib/TvIcon.svelte</text>
</g>
<!-- lib/UserOpenPrompt.svelte -->
<g id="node15" class="node">
<title>lib/UserOpenPrompt.svelte</title>
<path fill="none" stroke="#cfffac" d="M221.46,-611.75C221.46,-611.75 58.54,-611.75 58.54,-611.75 54.58,-611.75 50.62,-607.79 50.62,-603.83 50.62,-603.83 50.62,-595.92 50.62,-595.92 50.62,-591.96 54.58,-588 58.54,-588 58.54,-588 221.46,-588 221.46,-588 225.42,-588 229.38,-591.96 229.38,-595.92 229.38,-595.92 229.38,-603.83 229.38,-603.83 229.38,-607.79 225.42,-611.75 221.46,-611.75"/>
<text text-anchor="middle" x="140" y="-594.45" font-family="Arial" font-size="14.00" fill="#cfffac">lib/UserOpenPrompt.svelte</text>
</g>
<!-- routes/+layout.svelte -->
<g id="node16" class="node">
<title>routes/+layout.svelte</title>
<path fill="none" stroke="#cfffac" d="M202.33,-653.75C202.33,-653.75 77.67,-653.75 77.67,-653.75 73.71,-653.75 69.75,-649.79 69.75,-645.83 69.75,-645.83 69.75,-637.92 69.75,-637.92 69.75,-633.96 73.71,-630 77.67,-630 77.67,-630 202.33,-630 202.33,-630 206.29,-630 210.25,-633.96 210.25,-637.92 210.25,-637.92 210.25,-645.83 210.25,-645.83 210.25,-649.79 206.29,-653.75 202.33,-653.75"/>
<text text-anchor="middle" x="140" y="-636.45" font-family="Arial" font-size="14.00" fill="#cfffac">routes/+layout.svelte</text>
</g>
<!-- routes/+page.svelte -->
<g id="node17" class="node">
<title>routes/+page.svelte</title>
<path fill="none" stroke="#cfffac" d="M199.33,-695.75C199.33,-695.75 80.67,-695.75 80.67,-695.75 76.71,-695.75 72.75,-691.79 72.75,-687.83 72.75,-687.83 72.75,-679.92 72.75,-679.92 72.75,-675.96 76.71,-672 80.67,-672 80.67,-672 199.33,-672 199.33,-672 203.29,-672 207.25,-675.96 207.25,-679.92 207.25,-679.92 207.25,-687.83 207.25,-687.83 207.25,-691.79 203.29,-695.75 199.33,-695.75"/>
<text text-anchor="middle" x="140" y="-678.45" font-family="Arial" font-size="14.00" fill="#cfffac">routes/+page.svelte</text>
</g>
<!-- routes/api/getMediaDetails/+server.ts -->
<g id="node18" class="node">
<title>routes/api/getMediaDetails/+server.ts</title>
<path fill="none" stroke="#cfffac" d="M251.83,-737.75C251.83,-737.75 28.17,-737.75 28.17,-737.75 24.21,-737.75 20.25,-733.79 20.25,-729.83 20.25,-729.83 20.25,-721.92 20.25,-721.92 20.25,-717.96 24.21,-714 28.17,-714 28.17,-714 251.83,-714 251.83,-714 255.79,-714 259.75,-717.96 259.75,-721.92 259.75,-721.92 259.75,-729.83 259.75,-729.83 259.75,-733.79 255.79,-737.75 251.83,-737.75"/>
<text text-anchor="middle" x="140" y="-720.45" font-family="Arial" font-size="14.00" fill="#cfffac">routes/api/getMediaDetails/+server.ts</text>
</g>
<!-- routes/api/getRecommendation/+server.ts -->
<g id="node19" class="node">
<title>routes/api/getRecommendation/+server.ts</title>
<path fill="none" stroke="#cfffac" d="M266.08,-779.75C266.08,-779.75 13.92,-779.75 13.92,-779.75 9.96,-779.75 6,-775.79 6,-771.83 6,-771.83 6,-763.92 6,-763.92 6,-759.96 9.96,-756 13.92,-756 13.92,-756 266.08,-756 266.08,-756 270.04,-756 274,-759.96 274,-763.92 274,-763.92 274,-771.83 274,-771.83 274,-775.79 270.04,-779.75 266.08,-779.75"/>
<text text-anchor="middle" x="140" y="-762.45" font-family="Arial" font-size="14.00" fill="#cfffac">routes/api/getRecommendation/+server.ts</text>
</g>
</g>
</svg>
